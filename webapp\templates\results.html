{% extends "layout.html" %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- <PERSON> Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Grading Results</h1>
                <p class="mt-2 text-sm text-gray-600">
                    Comprehensive grading results and feedback for all student submissions.
                </p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ url_for('view_results', grouped='true') }}"
                    class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                    </svg>
                    Grouped View
                </a>
                <button onclick="exportResults()"
                    class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    Export Results
                </button>
                <a href="{{ url_for('dashboard') }}"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                    </svg>
                    Back to Dashboard
                </a>
            </div>
        </div>
    </div>

    {% if has_results %}
    <!-- Batch Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5 text-center">
                <div class="w-12 h-12 bg-primary-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                </div>
                <dl>
                    <dt class="text-sm font-medium text-gray-500 mb-1">Total Submissions</dt>
                    <dd class="text-lg font-medium text-gray-900">{{ successful_grades }}</dd>
                </dl>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5 text-center">
                <div class="w-12 h-12 bg-success-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                </div>
                <dl>
                    <dt class="text-sm font-medium text-gray-500 mb-1">Average Score</dt>
                    <dd class="text-lg font-medium text-gray-900">{{ batch_summary.average_score if batch_summary else 0
                        }}%</dd>
                </dl>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5 text-center">
                <div class="w-12 h-12 bg-warning-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                </div>
                <dl>
                    <dt class="text-sm font-medium text-gray-500 mb-1">Highest Score</dt>
                    <dd class="text-lg font-medium text-gray-900">{{ batch_summary.highest_score if batch_summary else 0
                        }}%</dd>
                </dl>
            </div>
        </div>

        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5 text-center">
                <div class="w-12 h-12 bg-info-500 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                    </svg>
                </div>
                <dl>
                    <dt class="text-sm font-medium text-gray-500 mb-1">Lowest Score</dt>
                    <dd class="text-lg font-medium text-gray-900">{{ batch_summary.lowest_score if batch_summary else 0
                        }}%</dd>
                </dl>
            </div>
        </div>
    </div>

    <!-- Individual Results Table -->
    <div class="bg-white shadow rounded-lg mb-8">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-6">Individual Submission Results</h3>

            <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
                <table class="min-w-full divide-y divide-gray-300">
                    <thead class="bg-gray-50">
                        <tr>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Submission
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Score
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Grade
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Questions
                            </th>
                            <th scope="col"
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Graded At
                            </th>
                            <th scope="col" class="relative px-6 py-3">
                                <span class="sr-only">Actions</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {% for result in results_list %}
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10">
                                        <div
                                            class="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                                            <svg class="h-5 w-5 text-primary-600" fill="none" stroke="currentColor"
                                                viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                    d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                            </svg>
                                        </div>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-gray-900">{{ result.filename }}</div>
                                        <div class="text-sm text-gray-500">ID: {{ result.submission_id[:8] }}...</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ result.score }}%</div>
                                <div class="w-16 bg-gray-200 rounded-full h-2 mt-1">
                                    <div class="h-2 rounded-full {% if result.score >= 80 %}bg-success-500{% elif result.score >= 60 %}bg-warning-500{% else %}bg-danger-500{% endif %}"
                                        style="width: {{ result.score }}%"></div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span
                                    class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {% if result.score >= 90 %}bg-success-100 text-success-800{% elif result.score >= 80 %}bg-success-100 text-success-800{% elif result.score >= 70 %}bg-warning-100 text-warning-800{% elif result.score >= 60 %}bg-warning-100 text-warning-800{% else %}bg-danger-100 text-danger-800{% endif %}">
                                    {{ result.letter_grade }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ result.total_questions }} questions
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                {{ result.graded_at[:10] if result.graded_at else 'Unknown' }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <button type="button" onclick="viewDetails('{{ result.submission_id }}')"
                                    class="text-primary-600 hover:text-primary-900 mr-3">
                                    View Details
                                </button>
                                <a href="{{ url_for('view_submission_content', submission_id=result.submission_id) }}"
                                    class="text-info-600 hover:text-info-900">
                                    View Content
                                </a>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    {% else %}
    <!-- No Results State -->
    <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No grading results available</h3>
        <p class="mt-1 text-sm text-gray-500">Upload submissions and complete the grading process to see results here.
        </p>
        <div class="mt-6">
            <a href="{{ url_for('upload_submission') }}"
                class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                Upload Submissions
            </a>
        </div>
    </div>
    {% endif %}

    <!-- Detailed Results Modal -->
    <div id="detailsModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog"
        aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"
                onclick="closeDetailsModal()"></div>

            <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

            <div
                class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
                <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="flex items-start justify-between">
                        <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                            Detailed Results
                        </h3>
                        <button type="button" onclick="closeDetailsModal()"
                            class="bg-white rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <span class="sr-only">Close</span>
                            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    <div id="modalContent" class="mt-4">
                        <!-- Content will be loaded here -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Make sure app.js is loaded -->
<script src="{{ url_for('static', filename='js/app.js') }}?v={{ range(1000, 9999) | random }}"></script>
<script>
// Create a simple notification manager if ExamGrader is not defined
if (typeof ExamGrader === 'undefined') {
    console.log('ExamGrader not defined, creating fallback notification manager');
    window.ExamGrader = {
        notificationManager: {
                notify: function (message, type) {
                console.log(`Notification (${type}): ${message}`);
                // Create a simple notification
                const notification = document.createElement('div');
                notification.className = `fixed top-4 right-4 p-4 rounded-md shadow-md ${type === 'success' ? 'bg-green-500' : 'bg-red-500'} text-white`;
                notification.textContent = message;
                document.body.appendChild(notification);
                
                // Remove after 3 seconds
                setTimeout(() => {
                    notification.remove();
                }, 3000);
            }
        }
    };
}

// Call initAutoRefresh when the page loads
    document.addEventListener('DOMContentLoaded', function () {
    console.log('DOM loaded, initializing auto-refresh');
    try {
        initAutoRefresh();
        console.log('Auto-refresh initialized successfully');
        
        // Trigger an immediate refresh to test functionality
        setTimeout(() => {
            console.log('Triggering initial refresh');
            refreshResults();
        }, 2000);
    } catch (error) {
        console.error('Error initializing auto-refresh:', error);
    }
    
    // Debug info
    console.log('Results data:', {
        hasResults: {{ has_results| tojson }},
        resultsCount: {{ results_list| length }},
        batchSummary: {{ batch_summary| tojson }}
    });
});

// Auto-refresh functionality
let autoRefreshEnabled = localStorage.getItem('results_auto_refresh') !== 'false';
let refreshInterval;
const REFRESH_INTERVAL_MS = 30000; // 30 seconds

// Initialize auto-refresh toggle
function initAutoRefresh() {
    // Add auto-refresh toggle to the header
    const headerDiv = document.querySelector('.flex.items-center.justify-between');
    if (headerDiv) {
        const toggleContainer = document.createElement('div');
        toggleContainer.className = 'flex items-center mr-4';
        toggleContainer.innerHTML = `
            <span class="text-sm text-gray-600 mr-2">Auto-refresh:</span>
            <label class="inline-flex items-center cursor-pointer">
                <input type="checkbox" id="auto-refresh-toggle" class="sr-only peer" ${autoRefreshEnabled ? 'checked' : ''}>
                <div class="relative w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
            </label>
            <span id="refresh-status" class="text-xs text-gray-500 ml-2 min-w-[80px]"></span>
        `;
        
        // Insert before the export button
        const buttonContainer = headerDiv.querySelector('.flex.space-x-3');
        if (buttonContainer) {
            headerDiv.insertBefore(toggleContainer, buttonContainer);
        }
        
        // Add event listener to toggle
        const toggle = document.getElementById('auto-refresh-toggle');
        if (toggle) {
                toggle.addEventListener('change', function () {
                autoRefreshEnabled = this.checked;
                localStorage.setItem('results_auto_refresh', autoRefreshEnabled);
                if (autoRefreshEnabled) {
                    startAutoRefresh();
                    ExamGrader.notificationManager.notify('Auto-refresh enabled', 'info');
                } else {
                    stopAutoRefresh();
                    ExamGrader.notificationManager.notify('Auto-refresh disabled', 'info');
                }
            });
        }
        
        // Start auto-refresh if enabled
        if (autoRefreshEnabled) {
            startAutoRefresh();
        }
    }
}

// Start auto-refresh
function startAutoRefresh() {
    stopAutoRefresh(); // Clear any existing interval
    
    const statusElement = document.getElementById('refresh-status');
    const toggleElement = document.getElementById('auto-refresh-toggle');
    let countdown = REFRESH_INTERVAL_MS / 1000;
    
    // Update UI elements
    if (statusElement) {
        statusElement.innerHTML = `<span class="text-primary-600">Refresh in ${countdown}s</span>`;
    }
    if (toggleElement) {
        toggleElement.checked = true;
    }
    
    // Set up countdown timer with visual feedback
    refreshInterval = setInterval(() => {
        countdown--;
        
        if (statusElement) {
            if (countdown <= 10) {
                // Add visual urgency for last 10 seconds
                statusElement.innerHTML = `<span class="text-warning-600 font-medium">Refresh in ${countdown}s</span>`;
            } else {
                statusElement.innerHTML = `<span class="text-primary-600">Refresh in ${countdown}s</span>`;
            }
        }
        
        if (countdown <= 0) {
            if (statusElement) {
                statusElement.innerHTML = '<span class="text-info-600">Refreshing...</span>';
            }
            // Refresh the page
            refreshResults();
            countdown = REFRESH_INTERVAL_MS / 1000;
        }
    }, 1000);
}

// Stop auto-refresh
function stopAutoRefresh() {
    if (refreshInterval) {
        clearInterval(refreshInterval);
        refreshInterval = null;
        
        const statusElement = document.getElementById('refresh-status');
        const toggleElement = document.getElementById('auto-refresh-toggle');
        
        // Update UI elements
        if (statusElement) {
            statusElement.innerHTML = '<span class="text-gray-500">Auto-refresh paused</span>';
        }
        if (toggleElement) {
            toggleElement.checked = false;
        }
        
        // Notify user
        if (typeof ExamGrader !== 'undefined' && ExamGrader.notificationManager) {
            ExamGrader.notificationManager.notify('Auto-refresh paused', 'info');
        }
    }
}

// Refresh results without reloading the page
async function refreshResults() {
    let retryCount = 0;
    const maxRetries = 3;
    const retryDelay = 2000; // 2 seconds

    async function attemptRefresh() {
        try {
            console.log(`Refresh attempt ${retryCount + 1} of ${maxRetries}`);
            const statusElement = document.getElementById('refresh-status');
            if (statusElement) {
                statusElement.textContent = 'Refreshing...';
            }
            
            // Fetch updated results data with timeout
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 10000); // 10 second timeout
            
            console.log('Fetching data from:', window.location.href);
            const response = await fetch(window.location.href, {
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache'
                },
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
        
            console.log('Response status:', response.status);
            if (!response.ok) {
                throw new Error(`HTTP error ${response.status}`);
            }
            
            // Check if response is JSON
            const contentType = response.headers.get('content-type');
            console.log('Response content type:', contentType);
            
            if (contentType && contentType.includes('application/json')) {
                const data = await response.json();
                console.log('Received JSON data:', data);
                updateResultsUI(data);
                if (typeof ExamGrader !== 'undefined' && ExamGrader.notificationManager) {
                    ExamGrader.notificationManager.notify('Results updated', 'success');
                } else {
                    console.warn('ExamGrader.notificationManager not available');
                }
                return true; // Successful refresh
            } else {
                throw new Error('Response is not JSON');
            }
        } catch (error) {
            console.error(`Refresh attempt ${retryCount + 1} failed:`, error);
            if (error.name === 'AbortError') {
                throw new Error('Request timed out');
            }
            throw error;
        }
    }

    while (retryCount < maxRetries) {
        try {
            await attemptRefresh();
            return; // Success, exit function
        } catch (error) {
            retryCount++;
            console.log(`Retry ${retryCount} of ${maxRetries}`);
            if (retryCount < maxRetries) {
                await new Promise(resolve => setTimeout(resolve, retryDelay));
            }
        }
    }

    // All retries failed
    console.error('All refresh attempts failed');
    if (typeof ExamGrader !== 'undefined' && ExamGrader.notificationManager) {
        ExamGrader.notificationManager.notify('Failed to refresh results after multiple attempts', 'error');
    }
    
    // Reset auto-refresh if all retries failed
    if (autoRefreshEnabled) {
        startAutoRefresh();
    }
    } catch (error) {
        console.error('Error in refresh process:', error);
        if (typeof ExamGrader !== 'undefined' && ExamGrader.notificationManager) {
            ExamGrader.notificationManager.notify(`Failed to refresh results: ${error.message}`, 'error');
        }
    } finally {
        const statusElement = document.getElementById('refresh-status');
        if (statusElement) {
            statusElement.textContent = autoRefreshEnabled ? 'Waiting...' : 'Paused';
        }
        console.log('Refresh process completed, autoRefreshEnabled:', autoRefreshEnabled);
        if (autoRefreshEnabled) {
            startAutoRefresh();
        }
    }
}

// Update UI with new results data
function updateResultsUI(data) {
    console.log('Updating UI with data:', data);
    
    if (!data || !data.success) {
        console.error('Invalid data received');
        return;
    }
    
    // Update batch summary cards
    if (data.batch_summary) {
        const summary = data.batch_summary;
        console.log('Updating summary cards with:', summary);
        
        try {
            updateElementText('.text-lg.font-medium.text-gray-900', 0, data.batch_summary.total_submissions);
            updateElementText('.text-lg.font-medium.text-gray-900', 1, `${summary.average_score}%`);
            updateElementText('.text-lg.font-medium.text-gray-900', 2, `${summary.highest_score}%`);
            updateElementText('.text-lg.font-medium.text-gray-900', 3, `${summary.lowest_score}%`);
            
            // Add last updated timestamp if not exists
            const headerDiv = document.querySelector('.flex.items-center.justify-between');
            let lastUpdatedEl = document.getElementById('last-updated-timestamp');
            if (!lastUpdatedEl && headerDiv) {
                lastUpdatedEl = document.createElement('div');
                lastUpdatedEl.id = 'last-updated-timestamp';
                lastUpdatedEl.className = 'text-xs text-gray-500 mt-1';
                headerDiv.querySelector('div').appendChild(lastUpdatedEl);
            }
            
            if (lastUpdatedEl) {
                lastUpdatedEl.textContent = `Last updated: ${summary.last_updated}`;
            }
        } catch (error) {
            console.error('Error updating summary cards:', error);
        }
    }
    
    // Update results table
    if (data.results && data.results.length > 0) {
        console.log('Updating results table with', data.results.length, 'rows');
        
        try {
            const tableBody = document.querySelector('table.min-w-full tbody');
            if (tableBody) {
                // Clear existing rows
                tableBody.innerHTML = '';
                
                // Add new rows
                data.results.forEach(result => {
                    console.log('Adding row for:', result.filename);
                    const row = document.createElement('tr');
                    row.className = 'bg-white hover:bg-gray-50';
                    
                    // Create cells based on your table structure
                    row.innerHTML = `
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center">
                                        <svg class="h-5 w-5 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                        </svg>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">${result.filename || 'Unknown'}</div>
                                    <div class="text-sm text-gray-500">ID: ${result.submission_id ? result.submission_id.substring(0, 8) : 'Unknown'}...</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-gray-900">${result.score || 0}%</div>
                            <div class="w-16 bg-gray-200 rounded-full h-2 mt-1">
                                <div class="h-2 rounded-full ${result.score >= 80 ? 'bg-success-500' : result.score >= 60 ? 'bg-warning-500' : 'bg-danger-500'}" style="width: ${result.score || 0}%"></div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${result.score >= 90 ? 'bg-success-100 text-success-800' : result.score >= 80 ? 'bg-success-100 text-success-800' : result.score >= 70 ? 'bg-warning-100 text-warning-800' : result.score >= 60 ? 'bg-warning-100 text-warning-800' : 'bg-danger-100 text-danger-800'}">
                                ${result.letter_grade || 'F'}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${result.total_questions || 0} questions
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            ${result.graded_at || ''}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button data-submission-id="${result.submission_id}" class="view-details-btn text-primary-600 hover:text-primary-900">View Details</button>
                        </td>
                    `;
                    
                    tableBody.appendChild(row);
                });
            } else {
                console.error('Table body element not found');
            }
        } catch (error) {
            console.error('Error updating results table:', error);
        }
    } else {
        console.log('No results to display');
    }
}

// Helper function to update text content of elements
function updateElementText(selector, index, text) {
    try {
        const elements = document.querySelectorAll(selector);
        console.log(`Found ${elements.length} elements matching ${selector}`);
        if (elements && elements[index]) {
            elements[index].textContent = text;
        } else {
            console.warn(`Element at index ${index} not found for selector ${selector}`);
        }
    } catch (error) {
        console.error(`Error updating element text for ${selector}:`, error);
    }
}

// Export results function
function exportResults() {
    // Call the API to export results
    fetch('/api/export-results')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const dataStr = JSON.stringify(data.data, null, 2);
                    const dataBlob = new Blob([dataStr], { type: 'application/json' });

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = data.filename;
                link.click();

                ExamGrader.notificationManager.notify('Results exported successfully!', 'success');
            } else {
                ExamGrader.notificationManager.notify('Failed to export results: ' + data.error, 'error');
            }
        })
        .catch(error => {
            console.error('Export error:', error);
            ExamGrader.notificationManager.notify('Failed to export results', 'error');
        });
}

window.viewDetails = function(submissionId) {
    // Find the result data for this submission
    const results = JSON.parse('{{ results_list|tojson|safe|replace("'", "\\'")|replace("`", "\\`") }}');
    const result = results.find(r => r.submission_id === submissionId);

    if (!result) {
        ExamGrader.notificationManager.notify('Result details not found', 'error');
        return;
    }

    // Build detailed content
    let content = `
        <div class="space-y-6">
            <div class="bg-gray-50 rounded-lg p-4">
                <h4 class="text-lg font-medium text-gray-900 mb-2">${result.filename}</h4>
                <div class="grid grid-cols-2 gap-4 text-sm">
                    <div>
                        <span class="font-medium text-gray-500">Score:</span>
                        <span class="ml-2 text-gray-900">${result.score}% (${result.letter_grade})</span>
                    </div>
                    <div>
                        <span class="font-medium text-gray-500">Questions:</span>
                        <span class="ml-2 text-gray-900">${result.total_questions}</span>
                    </div>
                </div>
            </div>

            <div>
                <h5 class="text-sm font-medium text-gray-700 mb-3">Question Breakdown:</h5>
                <div class="space-y-3">
    `;

    // Add question details
    if (result.criteria_scores) {
        result.criteria_scores.forEach((criteria, index) => {
            content += `
                <div class="border border-gray-200 rounded-lg p-3">
                    <div class="flex justify-between items-start mb-2">
                        <h6 class="text-sm font-medium text-gray-900">${criteria.description}</h6>
                        <span class="text-sm font-medium ${criteria.percentage >= 80 ? 'text-success-600' : criteria.percentage >= 60 ? 'text-warning-600' : 'text-danger-600'}">${criteria.points_earned}/${criteria.points_possible}</span>
                    </div>
                    <p class="text-xs text-gray-600">${criteria.feedback}</p>
                </div>
            `;
        });
    }

    content += `
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h5 class="text-sm font-medium text-success-700 mb-2">Strengths:</h5>
                    <ul class="text-sm text-gray-600 space-y-1">
    `;

    if (result.strengths) {
        result.strengths.forEach(strength => {
            content += `<li>• ${strength}</li>`;
        });
    }

    content += `
                    </ul>
                </div>
                <div>
                    <h5 class="text-sm font-medium text-warning-700 mb-2">Areas for Improvement:</h5>
                    <ul class="text-sm text-gray-600 space-y-1">
    `;

    if (result.weaknesses) {
        result.weaknesses.forEach(weakness => {
            content += `<li>• ${weakness}</li>`;
        });
    }

    content += `
                    </ul>
                </div>
            </div>

            <div>
                <h5 class="text-sm font-medium text-info-700 mb-2">Suggestions:</h5>
                <ul class="text-sm text-gray-600 space-y-1">
    `;

    if (result.suggestions) {
        result.suggestions.forEach(suggestion => {
            content += `<li>• ${suggestion}</li>`;
        });
    }

    content += `
                </ul>
            </div>
        </div>
    `;

    // Show modal
    document.getElementById('modalContent').innerHTML = content;
    document.getElementById('detailsModal').classList.remove('hidden');
}

window.closeDetailsModal = function() {
    document.getElementById('detailsModal').classList.add('hidden');
};

// Close modal on escape key
    document.addEventListener('keydown', function (event) {
    if (event.key === 'Escape') {
        closeDetailsModal();
    }
});

// Initialize auto-refresh when the page loads
    document.addEventListener('DOMContentLoaded', function () {
    initAutoRefresh();

    // Attach event listeners for view details buttons
    document.querySelectorAll('.view-details-btn').forEach(button => {
            button.addEventListener('click', function () {
            const submissionId = this.dataset.submissionId;
            viewDetails(submissionId);
        });
    });
});
</script>
{% endblock %}