{"headers": {"content_security_policy": "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; img-src 'self' data: https:; font-src 'self' https://cdn.jsdelivr.net https://cdnjs.cloudflare.com; connect-src 'self' ws: wss:; frame-ancestors 'none'; base-uri 'self'; form-action 'self'", "strict_transport_security": "", "x_content_type_options": "nosniff", "x_frame_options": "DENY", "x_xss_protection": "1; mode=block", "referrer_policy": "strict-origin-when-cross-origin", "permissions_policy": "geolocation=(), microphone=(), camera=(), payment=(), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=()", "cross_origin_embedder_policy": "require-corp", "cross_origin_opener_policy": "same-origin", "cross_origin_resource_policy": "same-origin"}, "file_upload": {"max_file_size": 52428800, "allowed_extensions": [".pdf", ".doc", ".docx", ".txt", ".rtf", ".odt", ".jpg", ".jpeg", ".png", ".gif", ".bmp", ".tiff", ".zip", ".rar", ".7z", ".tar", ".gz"], "blocked_extensions": [".exe", ".bat", ".cmd", ".com", ".scr", ".pif", ".vbs", ".js", ".jar", ".ps1", ".sh", ".php", ".asp", ".aspx", ".jsp", ".py", ".rb", ".pl"], "scan_for_malware": true, "quarantine_suspicious": true, "max_filename_length": 255, "allowed_mime_types": ["application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "text/plain", "text/rtf", "application/vnd.oasis.opendocument.text", "image/jpeg", "image/png", "image/gif", "image/bmp", "image/tiff", "application/zip", "application/x-rar-compressed", "application/x-7z-compressed", "application/x-tar", "application/gzip"], "virus_scan_timeout": 30, "content_validation": true}, "session": {"session_timeout_minutes": 30, "max_concurrent_sessions": 3, "session_cookie_secure": false, "session_cookie_httponly": true, "session_cookie_samesite": "Strict", "regenerate_session_on_login": true, "track_session_ip": true, "track_user_agent": true, "detect_session_hijacking": true, "session_encryption": true, "idle_timeout_minutes": 15, "absolute_timeout_hours": 8}, "authentication": {"max_failed_attempts": 10, "lockout_duration_minutes": 15, "password_min_length": 8, "password_max_length": 128, "require_uppercase": true, "require_lowercase": true, "require_digits": true, "require_special_chars": true, "password_history_count": 5, "password_expiry_days": 90, "force_password_change_on_first_login": true, "two_factor_authentication": false, "remember_me_duration_days": 30, "brute_force_protection": true, "captcha_after_failed_attempts": 3}, "rate_limiting": {"enabled": true, "requests_per_minute": 1000, "requests_per_hour": 1000, "requests_per_day": 10000, "burst_limit": 10, "whitelist_ips": [], "blacklist_ips": [], "rate_limit_by_ip": true, "rate_limit_by_user": true, "rate_limit_storage": "memory", "login_attempts_per_minute": 5, "upload_attempts_per_minute": 10, "api_calls_per_minute": 100}, "input_validation": {"max_input_length": 10000, "sanitize_html": true, "block_script_tags": true, "block_sql_injection": true, "block_path_traversal": true, "block_command_injection": true, "validate_json_schema": true, "max_json_depth": 10, "max_array_length": 1000, "unicode_normalization": true, "trim_whitespace": true}, "audit_logging": {"enabled": true, "log_authentication": true, "log_authorization": true, "log_file_operations": true, "log_admin_actions": true, "log_security_events": true, "log_failed_requests": true, "log_sensitive_data": false, "retention_days": 90, "log_format": "json", "log_level": "DEBUG", "separate_security_log": true}, "encryption": {"algorithm": "AES-256-GCM", "key_derivation": "PBKDF2", "key_iterations": 100000, "salt_length": 32, "iv_length": 16, "encrypt_session_data": true, "encrypt_file_storage": false, "encrypt_database_fields": ["password_hash", "email", "personal_info"], "key_rotation_days": 365}, "monitoring": {"enabled": true, "monitor_failed_logins": true, "monitor_privilege_escalation": true, "monitor_file_access": true, "monitor_suspicious_patterns": true, "alert_threshold_failed_logins": 10, "alert_threshold_time_window_minutes": 5, "email_alerts": true, "webhook_alerts": false, "alert_recipients": [], "real_time_monitoring": true}, "compliance": {"gdpr_compliance": true, "data_retention_days": 2555, "right_to_erasure": true, "data_portability": true, "consent_tracking": true, "privacy_by_design": true, "data_minimization": true, "purpose_limitation": true, "audit_trail_required": true}, "environment": "development", "debug_mode": true, "security_level": "high"}