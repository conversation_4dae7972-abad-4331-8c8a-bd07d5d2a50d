{% extends "auth_layout.html" %}

{% block title %}{{ page_title }} - Exam Grader{% endblock %}

{% block content %}
<!-- Signup Card -->
<div class="auth-card bg-white py-8 px-6 shadow-xl rounded-lg border border-gray-200">
    <!-- Header -->
    <div class="text-center mb-8">
        <div class="mx-auto h-12 w-12 flex items-center justify-center rounded-full bg-primary-100">
            <svg class="h-6 w-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z"></path>
            </svg>
        </div>
        <h2 class="mt-4 text-center text-3xl font-extrabold text-gray-900" data-i18n="create_account_title">
            Create Your Account
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600" data-i18n="create_account_subtitle">
            Join thousands of educators using AI-powered grading
        </p>
    </div>

    <!-- Flash Messages -->
    {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
    <div class="space-y-2 mb-6">
        {% for category, message in messages %}
        <div
            class="rounded-md p-4 {% if category == 'error' %}bg-red-50 border border-red-200{% elif category == 'success' %}bg-green-50 border border-green-200{% else %}bg-yellow-50 border border-yellow-200{% endif %}">
            <div class="flex">
                <div class="flex-shrink-0">
                    {% if category == 'error' %}
                    <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                            clip-rule="evenodd"></path>
                    </svg>
                    {% elif category == 'success' %}
                    <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd"></path>
                    </svg>
                    {% else %}
                    <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd"
                            d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                            clip-rule="evenodd"></path>
                    </svg>
                    {% endif %}
                </div>
                <div class="ml-3">
                    <p
                        class="text-sm {% if category == 'error' %}text-red-800{% elif category == 'success' %}text-green-800{% else %}text-yellow-800{% endif %}">
                        {{ message }}
                    </p>
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
    {% endif %}
    {% endwith %}

    <!-- Signup Form -->
    <form id="signupForm" class="space-y-6" action="{{ url_for('auth.signup') }}" method="POST">
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" />

        <div class="space-y-4">
            <!-- Username Field -->
            <div>
                <label for="username" class="block text-sm font-medium text-gray-700 mb-1" data-i18n="username">
                    Username
                </label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                    </div>
                    <input id="username" name="username" type="text" required
                        class="auth-input block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors sm:text-sm"
                        placeholder="{{ _('choose_username') }}" value="{{ username or '' }}" autocomplete="username">
                </div>
                <p class="mt-1 text-xs text-gray-500" data-i18n="username_requirements">At least 3 characters, letters,
                    numbers, hyphens, and underscores only</p>
            </div>

            <!-- Email Field -->
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-1" data-i18n="email_address">
                    Email Address
                </label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207">
                            </path>
                        </svg>
                    </div>
                    <input id="email" name="email" type="email" required
                        class="auth-input block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors sm:text-sm"
                        placeholder="{{ _('enter_email') }}" value="{{ email or '' }}" autocomplete="email">
                </div>
            </div>

            <!-- Password Field -->
            <div>
                <label for="password" class="block text-sm font-medium text-gray-700 mb-1" data-i18n="password">
                    Password
                </label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z">
                            </path>
                        </svg>
                    </div>
                    <input id="password" name="password" type="password" required
                        class="auth-input block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors sm:text-sm"
                        placeholder="{{ _('create_strong_password') }}" autocomplete="new-password">
                </div>
            </div>

            <!-- Confirm Password Field -->
            <div>
                <label for="confirm_password" class="block text-sm font-medium text-gray-700 mb-1"
                    data-i18n="confirm_password">
                    Confirm Password
                </label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z">
                            </path>
                        </svg>
                    </div>
                    <input id="confirm_password" name="confirm_password" type="password" required
                        class="auth-input block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-md placeholder-gray-500 text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-colors sm:text-sm"
                        placeholder="{{ _('confirm_your_password') }}" autocomplete="new-password">
                </div>
            </div>

            <!-- Terms and Conditions -->
            <div class="flex items-start">
                <div class="flex items-center h-5">
                    <input id="terms_accepted" name="terms_accepted" type="checkbox" required
                        class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded">
                </div>
                <div class="ml-3 text-sm">
                    <label for="terms_accepted" class="text-gray-700">
                        <span data-i18n="i_agree_to">I agree to the</span>
                        <a href="#" class="text-primary-600 hover:text-primary-500 font-medium"
                            data-i18n="terms_and_conditions">Terms and Conditions</a>
                        <span data-i18n="and">and</span>
                        <a href="#" class="text-primary-600 hover:text-primary-500 font-medium"
                            data-i18n="privacy_policy">Privacy Policy</a>
                    </label>
                </div>
            </div>
        </div>

        <!-- Submit Button -->
        <div>
            <button type="submit" id="submitBtn"
                class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-200 ease-in-out transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none">
                <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                    <svg class="h-5 w-5 text-primary-500 group-hover:text-primary-400 transition-colors"
                        fill="currentColor" viewBox="0 0 20 20">
                        <path
                            d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z">
                        </path>
                    </svg>
                </span>
                <span id="buttonText" data-i18n="create_account_button">Create Account</span>
            </button>
        </div>
    </form>
</div>

<!-- Additional Links -->
<div class="text-center mt-6">
    <p class="text-sm text-gray-600">
        <span data-i18n="already_have_account">Already have an account?</span>
        <a href="{{ url_for('auth.login') }}"
            class="font-medium text-primary-600 hover:text-primary-500 transition-colors" data-i18n="sign_in_here">
            Sign in here
        </a>
    </p>
    <p class="text-xs text-gray-500 mt-2">
        <a href="{{ url_for('landing') }}" class="hover:text-gray-700 transition-colors" data-i18n="back_to_homepage">
            ← Back to homepage
        </a>
    </p>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        const form = document.getElementById('signupForm');
        const usernameField = document.getElementById('username');
        const emailField = document.getElementById('email');
        const passwordField = document.getElementById('password');
        const confirmPasswordField = document.getElementById('confirm_password');
        const termsCheckbox = document.getElementById('terms_accepted');
        const submitBtn = document.getElementById('submitBtn');
        const buttonText = document.getElementById('buttonText');

        // Auto-focus on username field
        usernameField.focus();

        // Form validation
        function validateForm() {
            const username = usernameField.value.trim();
            const email = emailField.value.trim();
            const password = passwordField.value;
            const confirmPassword = confirmPasswordField.value;
            const termsAccepted = termsCheckbox.checked;

            const isUsernameValid = username.length >= 3 && /^[a-zA-Z0-9_-]+$/.test(username);
            const isEmailValid = email.includes('@') && email.includes('.');
            const isPasswordValid = password.length >= 8;
            const isPasswordMatchValid = password === confirmPassword;

            const isFormValid = isUsernameValid && isEmailValid && isPasswordValid &&
                isPasswordMatchValid && termsAccepted;

            submitBtn.disabled = !isFormValid;
            return isFormValid;
        }

        // Real-time validation
        [usernameField, emailField, passwordField, confirmPasswordField, termsCheckbox].forEach(field => {
            field.addEventListener('input', validateForm);
            field.addEventListener('change', validateForm);
        });

        // Form submission with loading state
        form.addEventListener('submit', function (e) {
            if (!validateForm()) {
                e.preventDefault();
                return false;
            }

            // Show loading state
            submitBtn.disabled = true;
            buttonText.textContent = ExamGrader.i18n.translate('creating_account');
        });

        // Initial validation
        validateForm();
    });
</script>
{% endblock %}