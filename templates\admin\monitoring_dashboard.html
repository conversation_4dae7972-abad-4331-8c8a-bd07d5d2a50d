<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>System Monitoring Dashboard - Exam Grader</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <style>
        .dashboard-card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            transition: transform 0.2s ease-in-out;
        }
        
        .dashboard-card:hover {
            transform: translateY(-2px);
        }
        
        .metric-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 12px;
            padding: 1.5rem;
            margin-bottom: 1rem;
        }
        
        .metric-value {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0.5rem;
        }
        
        .metric-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }
        
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        
        .status-healthy { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }
        
        .chart-container {
            position: relative;
            height: 300px;
            margin: 1rem 0;
        }
        
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .security-alert {
            border-left: 4px solid #dc3545;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        
        .performance-warning {
            border-left: 4px solid #ffc107;
            background-color: #fff3cd;
            border-color: #ffeaa7;
        }
        
        .loading-spinner {
            display: none;
            text-align: center;
            padding: 2rem;
        }
        
        .nav-pills .nav-link.active {
            background-color: #667eea;
        }
        
        .table-responsive {
            border-radius: 8px;
            overflow: hidden;
        }
        
        .badge-security {
            background-color: #6f42c1;
        }
        
        .badge-performance {
            background-color: #20c997;
        }
        
        .badge-system {
            background-color: #fd7e14;
        }
    </style>
</head>
<body class="bg-light">
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="fas fa-chart-line me-2"></i>
                System Monitoring Dashboard
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-clock me-1"></i>
                    <span id="current-time"></span>
                </span>
                <button class="btn btn-outline-light btn-sm" onclick="refreshAllData()">
                    <i class="fas fa-sync-alt me-1"></i>
                    Refresh
                </button>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- System Health Overview -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card dashboard-card">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-heartbeat me-2"></i>
                            System Health Overview
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="health-status" class="row">
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <div class="metric-value" id="overall-status">Loading...</div>
                                    <div class="metric-label">Overall Status</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <div class="metric-value" id="uptime">Loading...</div>
                                    <div class="metric-label">System Uptime</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <div class="metric-value" id="environment">Loading...</div>
                                    <div class="metric-label">Environment</div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="metric-card">
                                    <div class="metric-value" id="version">Loading...</div>
                                    <div class="metric-label">Version</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-12">
                                <h6>Component Health</h6>
                                <div id="component-health" class="row">
                                    <!-- Component health indicators will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <ul class="nav nav-pills mb-4" id="monitoring-tabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="security-tab" data-bs-toggle="pill" data-bs-target="#security-panel" type="button" role="tab">
                    <i class="fas fa-shield-alt me-2"></i>
                    Security
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="performance-tab" data-bs-toggle="pill" data-bs-target="#performance-panel" type="button" role="tab">
                    <i class="fas fa-tachometer-alt me-2"></i>
                    Performance
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="system-tab" data-bs-toggle="pill" data-bs-target="#system-panel" type="button" role="tab">
                    <i class="fas fa-server me-2"></i>
                    System Resources
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="maintenance-tab" data-bs-toggle="pill" data-bs-target="#maintenance-panel" type="button" role="tab">
                    <i class="fas fa-tools me-2"></i>
                    Maintenance
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="monitoring-content">
            <!-- Security Panel -->
            <div class="tab-pane fade show active" id="security-panel" role="tabpanel">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card dashboard-card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-lock me-2"></i>
                                    Security Status
                                </h6>
                            </div>
                            <div class="card-body">
                                <div id="security-status">
                                    <div class="loading-spinner">
                                        <div class="spinner-border" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card dashboard-card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    Security Events
                                </h6>
                            </div>
                            <div class="card-body">
                                <div id="security-events">
                                    <div class="loading-spinner">
                                        <div class="spinner-border" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Performance Panel -->
            <div class="tab-pane fade" id="performance-panel" role="tabpanel">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card dashboard-card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-area me-2"></i>
                                    Performance Metrics
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="performance-chart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card dashboard-card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-database me-2"></i>
                                    Cache Statistics
                                </h6>
                            </div>
                            <div class="card-body">
                                <div id="cache-stats">
                                    <div class="loading-spinner">
                                        <div class="spinner-border" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card dashboard-card mt-3">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-table me-2"></i>
                                    Database Performance
                                </h6>
                            </div>
                            <div class="card-body">
                                <div id="database-stats">
                                    <div class="loading-spinner">
                                        <div class="spinner-border" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Resources Panel -->
            <div class="tab-pane fade" id="system-panel" role="tabpanel">
                <div class="row">
                    <div class="col-12">
                        <div class="card dashboard-card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-microchip me-2"></i>
                                    System Resource Usage
                                </h6>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="resources-chart"></canvas>
                                </div>
                                <div id="resource-details" class="mt-3">
                                    <div class="loading-spinner">
                                        <div class="spinner-border" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Maintenance Panel -->
            <div class="tab-pane fade" id="maintenance-panel" role="tabpanel">
                <div class="row">
                    <div class="col-md-6">
                        <div class="card dashboard-card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-broom me-2"></i>
                                    Cache Management
                                </h6>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">Clear application cache to free up memory and ensure fresh data.</p>
                                <button class="btn btn-warning" onclick="clearCache()">
                                    <i class="fas fa-trash me-2"></i>
                                    Clear Cache
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card dashboard-card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-cogs me-2"></i>
                                    System Cleanup
                                </h6>
                            </div>
                            <div class="card-body">
                                <p class="text-muted">Perform maintenance cleanup tasks including garbage collection and file cleanup.</p>
                                <button class="btn btn-info" onclick="performCleanup()">
                                    <i class="fas fa-magic me-2"></i>
                                    Run Cleanup
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-3">
                    <div class="col-12">
                        <div class="card dashboard-card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-history me-2"></i>
                                    Maintenance Log
                                </h6>
                            </div>
                            <div class="card-body">
                                <div id="maintenance-log">
                                    <p class="text-muted">No maintenance activities recorded yet.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Refresh Button -->
    <button class="btn btn-primary btn-lg refresh-btn" onclick="refreshAllData()" title="Refresh All Data">
        <i class="fas fa-sync-alt"></i>
    </button>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // Global variables
        let performanceChart = null;
        let resourcesChart = null;
        let refreshInterval = null;
        
        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            updateCurrentTime();
            setInterval(updateCurrentTime, 1000);
            
            loadHealthStatus();
            loadSecurityStatus();
            loadPerformanceMetrics();
            loadSystemResources();
            
            // Auto-refresh every 30 seconds
            refreshInterval = setInterval(refreshAllData, 30000);
            
            // Tab change handlers
            document.querySelectorAll('[data-bs-toggle="pill"]').forEach(tab => {
                tab.addEventListener('shown.bs.tab', function(event) {
                    const target = event.target.getAttribute('data-bs-target');
                    if (target === '#performance-panel') {
                        loadPerformanceMetrics();
                    } else if (target === '#system-panel') {
                        loadSystemResources();
                    } else if (target === '#security-panel') {
                        loadSecurityStatus();
                        loadSecurityEvents();
                    }
                });
            });
        });
        
        function updateCurrentTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString();
        }
        
        function getCSRFToken() {
            return document.querySelector('meta[name="csrf-token"]').getAttribute('content');
        }
        
        async function apiRequest(url, options = {}) {
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCSRFToken()
                }
            };
            
            const response = await fetch(url, { ...defaultOptions, ...options });
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            return await response.json();
        }
        
        async function loadHealthStatus() {
            try {
                const data = await apiRequest('/api/monitoring/health');
                
                document.getElementById('overall-status').textContent = data.status.toUpperCase();
                document.getElementById('uptime').textContent = data.uptime || 'Unknown';
                document.getElementById('environment').textContent = data.environment.toUpperCase();
                document.getElementById('version').textContent = data.version;
                
                // Update component health
                const componentHealth = document.getElementById('component-health');
                componentHealth.innerHTML = '';
                
                Object.entries(data.checks).forEach(([component, status]) => {
                    const statusClass = status.healthy ? 'status-healthy' : 'status-error';
                    const statusText = status.healthy ? 'Healthy' : 'Error';
                    
                    componentHealth.innerHTML += `
                        <div class="col-md-3 mb-2">
                            <div class="d-flex align-items-center">
                                <span class="status-indicator ${statusClass}"></span>
                                <span class="fw-bold">${component.charAt(0).toUpperCase() + component.slice(1)}</span>
                                <span class="ms-auto badge bg-${status.healthy ? 'success' : 'danger'}">${statusText}</span>
                            </div>
                        </div>
                    `;
                });
                
            } catch (error) {
                console.error('Error loading health status:', error);
                showError('Failed to load health status');
            }
        }
        
        async function loadSecurityStatus() {
            try {
                const data = await apiRequest('/api/monitoring/security/status');
                const securityStatus = document.getElementById('security-status');
                
                if (data.success) {
                    const status = data.data;
                    securityStatus.innerHTML = `
                        <div class="row">
                            <div class="col-12 mb-3">
                                <h6>Security Level: <span class="badge badge-security">${status.security_level.toUpperCase()}</span></h6>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Security Features</h6>
                                <ul class="list-unstyled">
                                    ${Object.entries(status.features).map(([feature, enabled]) => 
                                        `<li><i class="fas fa-${enabled ? 'check text-success' : 'times text-danger'} me-2"></i>${feature.replace(/_/g, ' ').toUpperCase()}</li>`
                                    ).join('')}
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6>Session Configuration</h6>
                                <ul class="list-unstyled">
                                    <li><strong>Timeout:</strong> ${status.session_config.timeout_minutes} minutes</li>
                                    <li><strong>Secure Cookies:</strong> ${status.session_config.secure_cookies ? 'Yes' : 'No'}</li>
                                    <li><strong>HTTP Only:</strong> ${status.session_config.httponly_cookies ? 'Yes' : 'No'}</li>
                                </ul>
                            </div>
                        </div>
                    `;
                } else {
                    securityStatus.innerHTML = `<div class="alert alert-danger">Error: ${data.error}</div>`;
                }
                
            } catch (error) {
                console.error('Error loading security status:', error);
                document.getElementById('security-status').innerHTML = '<div class="alert alert-danger">Failed to load security status</div>';
            }
        }
        
        async function loadSecurityEvents() {
            try {
                const data = await apiRequest('/api/monitoring/security/events');
                const securityEvents = document.getElementById('security-events');
                
                if (data.success) {
                    const events = data.data;
                    securityEvents.innerHTML = `
                        <div class="row mb-3">
                            <div class="col-6">
                                <div class="text-center">
                                    <div class="h4 text-danger">${events.summary.failed_logins}</div>
                                    <small class="text-muted">Failed Logins</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <div class="h4 text-warning">${events.summary.blocked_requests}</div>
                                    <small class="text-muted">Blocked Requests</small>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-6">
                                <div class="text-center">
                                    <div class="h4 text-info">${events.summary.quarantined_files}</div>
                                    <small class="text-muted">Quarantined Files</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <div class="h4 text-primary">${events.summary.security_violations}</div>
                                    <small class="text-muted">Security Violations</small>
                                </div>
                            </div>
                        </div>
                        <div class="mt-3">
                            <small class="text-muted">Last ${events.time_range.hours} hours</small>
                        </div>
                    `;
                } else {
                    securityEvents.innerHTML = `<div class="alert alert-danger">Error: ${data.error}</div>`;
                }
                
            } catch (error) {
                console.error('Error loading security events:', error);
                document.getElementById('security-events').innerHTML = '<div class="alert alert-danger">Failed to load security events</div>';
            }
        }
        
        async function loadPerformanceMetrics() {
            try {
                const data = await apiRequest('/api/monitoring/performance/metrics');
                
                if (data.success) {
                    updatePerformanceChart(data.data);
                    await loadCacheStats();
                    await loadDatabaseStats();
                } else {
                    showError('Failed to load performance metrics: ' + data.error);
                }
                
            } catch (error) {
                console.error('Error loading performance metrics:', error);
                showError('Failed to load performance metrics');
            }
        }
        
        async function loadCacheStats() {
            try {
                const data = await apiRequest('/api/monitoring/performance/cache/stats');
                const cacheStats = document.getElementById('cache-stats');
                
                if (data.success) {
                    const stats = data.data;
                    cacheStats.innerHTML = `
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Hit Rate</span>
                                <span class="fw-bold">${(stats.hit_rate * 100).toFixed(1)}%</span>
                            </div>
                            <div class="progress mt-1">
                                <div class="progress-bar" style="width: ${stats.hit_rate * 100}%"></div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Total Hits</span>
                                <span class="fw-bold">${stats.total_hits}</span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Total Misses</span>
                                <span class="fw-bold">${stats.total_misses}</span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Memory Cache Size</span>
                                <span class="fw-bold">${stats.memory_cache.size}</span>
                            </div>
                        </div>
                        <div>
                            <div class="d-flex justify-content-between">
                                <span>Redis Available</span>
                                <span class="badge bg-${stats.redis_available ? 'success' : 'warning'}">
                                    ${stats.redis_available ? 'Yes' : 'No'}
                                </span>
                            </div>
                        </div>
                    `;
                } else {
                    cacheStats.innerHTML = `<div class="alert alert-danger">Error: ${data.error}</div>`;
                }
                
            } catch (error) {
                console.error('Error loading cache stats:', error);
                document.getElementById('cache-stats').innerHTML = '<div class="alert alert-danger">Failed to load cache statistics</div>';
            }
        }
        
        async function loadDatabaseStats() {
            try {
                const data = await apiRequest('/api/monitoring/performance/database/stats');
                const databaseStats = document.getElementById('database-stats');
                
                if (data.success) {
                    const stats = data.data;
                    databaseStats.innerHTML = `
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Avg Query Time</span>
                                <span class="fw-bold">${stats.average_query_time_ms?.toFixed(2) || 'N/A'} ms</span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Total Queries</span>
                                <span class="fw-bold">${stats.total_queries || 0}</span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="d-flex justify-content-between">
                                <span>Slow Queries</span>
                                <span class="fw-bold text-warning">${stats.slow_queries || 0}</span>
                            </div>
                        </div>
                        <div>
                            <div class="d-flex justify-content-between">
                                <span>Active Connections</span>
                                <span class="fw-bold">${stats.active_connections || 0}</span>
                            </div>
                        </div>
                    `;
                } else {
                    databaseStats.innerHTML = `<div class="alert alert-danger">Error: ${data.error}</div>`;
                }
                
            } catch (error) {
                console.error('Error loading database stats:', error);
                document.getElementById('database-stats').innerHTML = '<div class="alert alert-danger">Failed to load database statistics</div>';
            }
        }
        
        async function loadSystemResources() {
            try {
                const data = await apiRequest('/api/monitoring/system/resources');
                
                if (data.success) {
                    updateResourcesChart(data.data);
                    updateResourceDetails(data.data.current);
                } else {
                    showError('Failed to load system resources: ' + data.error);
                }
                
            } catch (error) {
                console.error('Error loading system resources:', error);
                showError('Failed to load system resources');
            }
        }
        
        function updatePerformanceChart(data) {
            const ctx = document.getElementById('performance-chart').getContext('2d');
            
            if (performanceChart) {
                performanceChart.destroy();
            }
            
            performanceChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['1h ago', '45m ago', '30m ago', '15m ago', 'Now'],
                    datasets: [{
                        label: 'Response Time (ms)',
                        data: [120, 110, 95, 105, data.average_response_time_ms || 100],
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }, {
                        label: 'Requests/min',
                        data: [45, 52, 48, 55, data.requests_per_minute || 50],
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        tension: 0.1,
                        yAxisID: 'y1'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            grid: {
                                drawOnChartArea: false,
                            },
                        }
                    }
                }
            });
        }
        
        function updateResourcesChart(data) {
            const ctx = document.getElementById('resources-chart').getContext('2d');
            
            if (resourcesChart) {
                resourcesChart.destroy();
            }
            
            const current = data.current;
            const history = data.history || [];
            
            resourcesChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: history.map((_, index) => `${history.length - index}m ago`).concat(['Now']),
                    datasets: [{
                        label: 'CPU Usage (%)',
                        data: history.map(h => h.system_cpu_percent).concat([current.system_cpu_percent || 0]),
                        borderColor: 'rgb(255, 206, 86)',
                        backgroundColor: 'rgba(255, 206, 86, 0.2)',
                        tension: 0.1
                    }, {
                        label: 'Memory Usage (%)',
                        data: history.map(h => h.system_memory_percent).concat([current.system_memory_percent || 0]),
                        borderColor: 'rgb(54, 162, 235)',
                        backgroundColor: 'rgba(54, 162, 235, 0.2)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });
        }
        
        function updateResourceDetails(current) {
            const resourceDetails = document.getElementById('resource-details');
            
            resourceDetails.innerHTML = `
                <div class="row">
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4">${current.system_cpu_percent?.toFixed(1) || 'N/A'}%</div>
                            <small class="text-muted">System CPU</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4">${current.system_memory_percent?.toFixed(1) || 'N/A'}%</div>
                            <small class="text-muted">System Memory</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4">${current.process_cpu_percent?.toFixed(1) || 'N/A'}%</div>
                            <small class="text-muted">Process CPU</small>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="text-center">
                            <div class="h4">${current.process_memory_mb?.toFixed(0) || 'N/A'} MB</div>
                            <small class="text-muted">Process Memory</small>
                        </div>
                    </div>
                </div>
            `;
        }
        
        async function clearCache() {
            try {
                const data = await apiRequest('/api/monitoring/cache/clear', {
                    method: 'POST'
                });
                
                if (data.success) {
                    showSuccess('Cache cleared successfully');
                    logMaintenanceActivity('Cache cleared', data);
                } else {
                    showError('Failed to clear cache: ' + data.error);
                }
                
            } catch (error) {
                console.error('Error clearing cache:', error);
                showError('Failed to clear cache');
            }
        }
        
        async function performCleanup() {
            try {
                const data = await apiRequest('/api/monitoring/maintenance/cleanup', {
                    method: 'POST'
                });
                
                if (data.success) {
                    showSuccess('Cleanup completed successfully');
                    logMaintenanceActivity('System cleanup', data.data);
                } else {
                    showError('Failed to perform cleanup: ' + data.error);
                }
                
            } catch (error) {
                console.error('Error performing cleanup:', error);
                showError('Failed to perform cleanup');
            }
        }
        
        function logMaintenanceActivity(activity, details) {
            const log = document.getElementById('maintenance-log');
            const timestamp = new Date().toLocaleString();
            
            const logEntry = document.createElement('div');
            logEntry.className = 'alert alert-info mb-2';
            logEntry.innerHTML = `
                <div class="d-flex justify-content-between">
                    <span><strong>${activity}</strong></span>
                    <small>${timestamp}</small>
                </div>
                <div class="mt-1">
                    <small>${JSON.stringify(details, null, 2)}</small>
                </div>
            `;
            
            if (log.querySelector('.text-muted')) {
                log.innerHTML = '';
            }
            
            log.insertBefore(logEntry, log.firstChild);
            
            // Keep only last 10 entries
            const entries = log.querySelectorAll('.alert');
            if (entries.length > 10) {
                entries[entries.length - 1].remove();
            }
        }
        
        function refreshAllData() {
            loadHealthStatus();
            
            const activeTab = document.querySelector('.nav-link.active').getAttribute('data-bs-target');
            
            if (activeTab === '#security-panel') {
                loadSecurityStatus();
                loadSecurityEvents();
            } else if (activeTab === '#performance-panel') {
                loadPerformanceMetrics();
            } else if (activeTab === '#system-panel') {
                loadSystemResources();
            }
        }
        
        function showSuccess(message) {
            showToast(message, 'success');
        }
        
        function showError(message) {
            showToast(message, 'danger');
        }
        
        function showToast(message, type) {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 5000);
        }
    </script>
</body>
</html>