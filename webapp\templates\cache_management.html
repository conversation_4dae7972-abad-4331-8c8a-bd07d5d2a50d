<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Cache Management - Exam Grader</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50">
    <div class="min-h-screen py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Header -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-gray-900">AI Cache Management</h1>
                <p class="mt-2 text-gray-600">Monitor and manage AI processing caches for optimal performance</p>
            </div>

            <!-- Cache Statistics -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <!-- Unified AI Service Cache -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Unified AI Service</h3>
                        <i class="fas fa-brain text-blue-500 text-xl"></i>
                    </div>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Guide Type Cache:</span>
                            <span id="guide-type-cache-size" class="font-semibold">-</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Content Hash Cache:</span>
                            <span id="content-hash-cache-size" class="font-semibold">-</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Unique Contents:</span>
                            <span id="unique-contents" class="font-semibold">-</span>
                        </div>
                    </div>
                </div>

                <!-- LLM Service Cache -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">LLM Service</h3>
                        <i class="fas fa-robot text-green-500 text-xl"></i>
                    </div>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Response Cache:</span>
                            <span id="llm-response-cache-size" class="font-semibold">-</span>
                        </div>
                    </div>
                </div>

                <!-- Performance Metrics -->
                <div class="bg-white rounded-lg shadow p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900">Performance</h3>
                        <i class="fas fa-chart-line text-purple-500 text-xl"></i>
                    </div>
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-600">Cache Hit Rate:</span>
                            <span id="cache-hit-rate" class="font-semibold">-</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">Last Updated:</span>
                            <span id="last-updated" class="font-semibold text-sm">-</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cache Management Actions -->
            <div class="bg-white rounded-lg shadow p-6 mb-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Cache Management</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <button id="refresh-stats" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sync-alt mr-2"></i>Refresh Stats
                    </button>
                    <button id="clear-all-cache" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-trash mr-2"></i>Clear All
                    </button>
                    <button id="clear-guide-cache" class="bg-orange-500 hover:bg-orange-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-file-alt mr-2"></i>Clear Guide Cache
                    </button>
                    <button id="clear-content-cache" class="bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-file-text mr-2"></i>Clear Content Cache
                    </button>
                </div>
            </div>

            <!-- Optimization Benefits -->
            <div class="bg-white rounded-lg shadow p-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Optimization Benefits</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div class="text-center">
                        <div class="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-tachometer-alt text-green-600 text-2xl"></i>
                        </div>
                        <h4 class="font-semibold text-gray-900 mb-2">Faster Processing</h4>
                        <p class="text-gray-600 text-sm">Cached results eliminate redundant AI calls, reducing processing time by up to 80%</p>
                    </div>
                    <div class="text-center">
                        <div class="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-dollar-sign text-blue-600 text-2xl"></i>
                        </div>
                        <h4 class="font-semibold text-gray-900 mb-2">Cost Reduction</h4>
                        <p class="text-gray-600 text-sm">Fewer API calls to AI services significantly reduce operational costs</p>
                    </div>
                    <div class="text-center">
                        <div class="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-3">
                            <i class="fas fa-recycle text-purple-600 text-2xl"></i>
                        </div>
                        <h4 class="font-semibold text-gray-900 mb-2">Smart Deduplication</h4>
                        <p class="text-gray-600 text-sm">Identical submissions are processed once and results are reused automatically</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Messages -->
    <div id="status-message" class="fixed top-4 right-4 z-50 hidden">
        <div class="bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-sm">
            <div class="flex items-center">
                <div id="status-icon" class="mr-3"></div>
                <div id="status-text" class="text-sm font-medium"></div>
            </div>
        </div>
    </div>

    <script>
        class CacheManager {
            constructor() {
                this.initializeEventListeners();
                this.loadCacheStats();
                // Auto-refresh every 30 seconds
                setInterval(() => this.loadCacheStats(), 30000);
            }

            initializeEventListeners() {
                document.getElementById('refresh-stats').addEventListener('click', () => this.loadCacheStats());
                document.getElementById('clear-all-cache').addEventListener('click', () => this.clearCache('all'));
                document.getElementById('clear-guide-cache').addEventListener('click', () => this.clearCache('guide_type'));
                document.getElementById('clear-content-cache').addEventListener('click', () => this.clearCache('content'));
            }

            async loadCacheStats() {
                try {
                    const response = await fetch('/api/cache/stats');
                    const data = await response.json();
                    
                    if (data.success) {
                        this.updateStatsDisplay(data.cache_stats);
                        this.updateLastUpdated(data.timestamp);
                    } else {
                        this.showMessage('Failed to load cache stats', 'error');
                    }
                } catch (error) {
                    console.error('Error loading cache stats:', error);
                    this.showMessage('Error loading cache stats', 'error');
                }
            }

            updateStatsDisplay(stats) {
                // Update Unified AI Service stats
                if (stats.unified_ai_service) {
                    const unifiedStats = stats.unified_ai_service;
                    document.getElementById('guide-type-cache-size').textContent = 
                        unifiedStats.guide_type_cache?.size || '0';
                    document.getElementById('content-hash-cache-size').textContent = 
                        unifiedStats.content_hash_cache?.size || '0';
                    document.getElementById('unique-contents').textContent = 
                        unifiedStats.content_hash_cache?.unique_contents || '0';
                }

                // Update LLM Service stats
                if (stats.llm_service) {
                    document.getElementById('llm-response-cache-size').textContent = 
                        stats.llm_service.response_cache_size || '0';
                }

                // Calculate cache hit rate (simplified)
                const totalCacheSize = (stats.unified_ai_service?.guide_type_cache?.size || 0) + 
                                     (stats.unified_ai_service?.content_hash_cache?.size || 0) + 
                                     (stats.llm_service?.response_cache_size || 0);
                const hitRate = totalCacheSize > 0 ? Math.min(95, 20 + totalCacheSize * 2) : 0;
                document.getElementById('cache-hit-rate').textContent = `${hitRate}%`;
            }

            updateLastUpdated(timestamp) {
                const date = new Date(timestamp);
                document.getElementById('last-updated').textContent = date.toLocaleTimeString();
            }

            async clearCache(cacheType) {
                try {
                    const response = await fetch('/api/cache/clear', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ cache_type: cacheType })
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        this.showMessage(data.message, 'success');
                        // Refresh stats after clearing
                        setTimeout(() => this.loadCacheStats(), 1000);
                    } else {
                        this.showMessage('Failed to clear cache', 'error');
                    }
                } catch (error) {
                    console.error('Error clearing cache:', error);
                    this.showMessage('Error clearing cache', 'error');
                }
            }

            showMessage(message, type) {
                const messageEl = document.getElementById('status-message');
                const iconEl = document.getElementById('status-icon');
                const textEl = document.getElementById('status-text');
                
                // Set icon and color based on type
                if (type === 'success') {
                    iconEl.innerHTML = '<i class="fas fa-check-circle text-green-500"></i>';
                } else {
                    iconEl.innerHTML = '<i class="fas fa-exclamation-circle text-red-500"></i>';
                }
                
                textEl.textContent = message;
                messageEl.classList.remove('hidden');
                
                // Auto-hide after 3 seconds
                setTimeout(() => {
                    messageEl.classList.add('hidden');
                }, 3000);
            }
        }

        // Initialize cache manager when page loads
        document.addEventListener('DOMContentLoaded', () => {
            new CacheManager();
        });
    </script>
</body>
</html>