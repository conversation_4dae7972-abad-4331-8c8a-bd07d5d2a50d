{% extends "layout.html" %}

{% block content %}
<style>
    /* Enhanced two-column layout for processing interface */
    .processing-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .processing-grid {
            grid-template-columns: 1fr;
            gap: 1.5rem;
        }
    }

    /* Ensure cards have consistent height */
    .processing-card {
        height: fit-content;
    }

    /* Processing steps always in one row */
    .processing-steps {
        display: grid;
        grid-template-columns: 1fr 1fr 1fr;
        gap: 1.5rem;
    }

    /* Responsive adjustments for processing steps */
    @media (max-width: 640px) {
        .processing-steps {
            gap: 1rem;
        }

        .processing-steps .text-center {
            font-size: 0.875rem;
        }

        .processing-steps h3 {
            font-size: 1rem;
        }

        .processing-steps p {
            font-size: 0.75rem;
        }
    }
</style>
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Hidden CSRF Token -->
    <input type="hidden" name="csrf_token" value="{{ csrf_token }}" />
    <!-- <PERSON> Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Enhanced AI Processing Pipeline</h1>
        <p class="mt-2 text-lg text-gray-600">
            Complete LLM-driven workflow for marking guide processing, submission mapping, and intelligent grading
        </p>
    </div>

    <!-- Processing Steps Overview -->
    <div class="mb-8">
        <div class="bg-white shadow rounded-lg p-6">
            <h2 class="text-xl font-semibold text-gray-900 mb-4">Processing Pipeline</h2>
            <div class="processing-steps">
                <div class="text-center">
                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z">
                            </path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900">1. Guide Processing</h3>
                    <p class="text-sm text-gray-500">LLM extracts structured questions and answers from marking guides
                    </p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900">2. Submission Mapping</h3>
                    <p class="text-sm text-gray-500">OCR + LLM intelligently maps student answers to guide questions</p>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                            </path>
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900">3. AI Grading</h3>
                    <p class="text-sm text-gray-500">LLM grades with max_questions_to_answer logic and detailed feedback
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Processing Interface -->
    <div class="processing-grid">
        <!-- Left Column: Guide Selection and Processing -->
        <div class="space-y-6">
            <!-- Marking Guide Selection -->
            <div class="bg-white shadow rounded-lg p-6 processing-card">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Select Marking Guide</h3>
                <div class="space-y-4">
                    <select id="markingGuideSelect"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">Loading marking guides...</option>
                    </select>
                    <div id="guideInfo" class="hidden bg-gray-50 p-4 rounded-md">
                        <h4 class="font-medium text-gray-900">Guide Information</h4>
                        <div class="mt-2 text-sm text-gray-600">
                            <p><span class="font-medium">Title:</span> <span id="guideTitle"></span></p>
                            <p><span class="font-medium">Questions:</span> <span id="guideQuestions"></span></p>
                            <p><span class="font-medium">Total Marks:</span> <span id="guideTotalMarks"></span></p>
                            <p><span class="font-medium">Status:</span> <span id="guideStatus"></span></p>
                        </div>
                    </div>

                </div>
            </div>

            <!-- Submission Selection -->
            <div class="bg-white shadow rounded-lg p-6 processing-card">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Select Submissions</h3>
                <div class="space-y-4">
                    <div class="max-h-64 overflow-y-auto border border-gray-200 rounded-md">
                        <div id="submissionsList" class="divide-y divide-gray-200">
                            <p class="p-4 text-gray-500 text-center">Select a marking guide first</p>
                        </div>
                    </div>
                    <div class="flex items-center justify-between">
                        <label class="flex items-center">
                            <input type="checkbox" id="selectAllSubmissions"
                                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-700">Select All</span>
                        </label>
                        <span id="selectedCount" class="text-sm text-gray-500">0 selected</span>
                    </div>
                </div>
            </div>

            <!-- Processing Options -->
            <div class="bg-white shadow rounded-lg p-6 processing-card">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Processing Options</h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Max Questions to Answer</label>
                        <input type="number" id="maxQuestions" min="1" max="50"
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="Leave empty for all questions">
                        <p class="mt-1 text-xs text-gray-500">Limit the number of questions to grade (best answers will
                            be selected)</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Processing Steps</label>
                        <div class="space-y-2">
                            <label class="flex items-center">
                                <input type="checkbox" id="processGradingStep" checked
                                    class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-700">Process Grading</span>
                            </label>
                            <p class="text-xs text-gray-500 mt-2">
                                Note: Marking guides and submissions have already been processed and saved to the
                                database.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Right Column: Processing Status and Results -->
        <div class="space-y-6">
            <!-- Processing Controls -->
            <div class="bg-white shadow rounded-lg p-6 processing-card">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Processing Controls</h3>
                <div class="space-y-4">
                    <button id="startBatchProcessing"
                        class="w-full bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed">
                        Start Batch Processing
                    </button>
                    <button id="stopProcessing"
                        class="w-full bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
                        disabled>
                        Stop Processing
                    </button>
                </div>
            </div>

            <!-- Processing Status -->
            <div class="bg-white shadow rounded-lg p-6 processing-card">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Processing Status</h3>
                <div id="processingStatus" class="space-y-4">
                    <div class="text-center text-gray-500 py-8">
                        <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2">
                            </path>
                        </svg>
                        <p>No processing in progress</p>
                    </div>
                </div>
            </div>

            <!-- Results Summary -->
            <div class="bg-white shadow rounded-lg p-6 processing-card">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Results Summary</h3>
                <div id="resultsSummary" class="space-y-4">
                    <div class="text-center text-gray-500 py-8">
                        <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z">
                            </path>
                        </svg>
                        <p>No results available</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Results Table -->
    <div class="mt-8">
        <div class="bg-white shadow rounded-lg">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Detailed Results</h3>
            </div>
            <div class="overflow-x-auto">
                <table id="resultsTable" class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Student</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Submission</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Processing Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Score</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Grade</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Questions Graded</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions</th>
                        </tr>
                    </thead>
                    <tbody id="resultsTableBody" class="bg-white divide-y divide-gray-200">
                        <tr>
                            <td colspan="7" class="px-6 py-4 text-center text-gray-500">No results to display</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Processing Modal -->
<div id="processingModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-blue-100">
                <svg class="animate-spin h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                    </path>
                </svg>
            </div>
            <h3 class="text-lg leading-6 font-medium text-gray-900 mt-4" id="modalTitle">Processing...</h3>
            <div class="mt-2 px-7 py-3">
                <p class="text-sm text-gray-500" id="modalMessage">Please wait while we process your request.</p>
                <div class="mt-4">
                    <div class="bg-gray-200 rounded-full h-2">
                        <div id="modalProgress" class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style="width: 0%"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    class EnhancedProcessingManager {
        constructor() {
            this.selectedGuideId = null;
            this.selectedSubmissions = new Set();
            this.isProcessing = false;
            this.processingInterval = null;

            this.initializeEventListeners();
            // Delay loading marking guides to ensure page is fully loaded and authenticated
            setTimeout(() => {
                this.loadMarkingGuides();
            }, 500);
        }

        initializeEventListeners() {
            // Guide selection
            document.getElementById('markingGuideSelect').addEventListener('change', (e) => {
                this.selectMarkingGuide(e.target.value);
            });



            // Select all submissions
            document.getElementById('selectAllSubmissions').addEventListener('change', (e) => {
                this.toggleAllSubmissions(e.target.checked);
            });

            // Batch processing
            document.getElementById('startBatchProcessing').addEventListener('click', () => {
                this.startBatchProcessing();
            });

            document.getElementById('stopProcessing').addEventListener('click', () => {
                this.stopProcessing();
            });
        }

        async loadMarkingGuides() {
            try {
                const response = await fetch('/api/marking-guides');

                // Check if response is ok and content type is JSON
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const contentType = response.headers.get('content-type');
                if (!contentType || !contentType.includes('application/json')) {
                    throw new Error('Response is not JSON - you may need to log in again');
                }

                const data = await response.json();

                const select = document.getElementById('markingGuideSelect');
                select.innerHTML = '<option value="">Select a marking guide...</option>';

                if (data.success && data.guides) {
                    data.guides.forEach(guide => {
                        const option = document.createElement('option');
                        option.value = guide.id;
                        option.textContent = `${guide.title} (${guide.questions_count || 0} questions)`;
                        select.appendChild(option);
                    });
                } else {
                    this.showError(data.error || 'No marking guides found');
                }
            } catch (error) {
                console.error('Error loading marking guides:', error);
                if (error.message.includes('not JSON')) {
                    this.showAuthenticationError();
                } else {
                    this.showError('Failed to load marking guides: ' + error.message);
                }
            }
        }

        async selectMarkingGuide(guideId) {
            this.selectedGuideId = guideId;

            if (!guideId) {
                document.getElementById('guideInfo').classList.add('hidden');
                document.getElementById('submissionsList').innerHTML = '<p class="p-4 text-gray-500 text-center">Select a marking guide first</p>';
                return;
            }

            try {
                // Load guide info
                const guideResponse = await fetch(`/api/marking-guides/${guideId}`);
                const guideData = await guideResponse.json();

                if (guideData.success) {
                    this.displayGuideInfo(guideData.guide);
                }

                // Load submissions for this guide
                const submissionsResponse = await fetch(`/api/submissions?marking_guide_id=${guideId}`);
                const submissionsData = await submissionsResponse.json();

                if (submissionsData.success) {
                    this.displaySubmissions(submissionsData.submissions);
                }
            } catch (error) {
                console.error('Error loading guide data:', error);
                this.showError('Failed to load guide information');
            }
        }

        displayGuideInfo(guide) {
            document.getElementById('guideTitle').textContent = guide.title;
            document.getElementById('guideQuestions').textContent = guide.questions_count || 0;
            document.getElementById('guideTotalMarks').textContent = guide.total_marks || 0;
            document.getElementById('guideStatus').textContent = guide.questions ? 'Processed' : 'Not Processed';
            document.getElementById('guideInfo').classList.remove('hidden');
        }

        displaySubmissions(submissions) {
            const container = document.getElementById('submissionsList');

            if (!submissions || submissions.length === 0) {
                container.innerHTML = '<p class="p-4 text-gray-500 text-center">No submissions found for this guide</p>';
                return;
            }

            container.innerHTML = '';
            submissions.forEach(submission => {
                const div = document.createElement('div');
                div.className = 'p-4 hover:bg-gray-50';
                div.innerHTML = `
                <label class="flex items-center cursor-pointer">
                    <input type="checkbox" class="submission-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500" 
                           value="${submission.id}" data-student-name="${submission.student_name}" data-filename="${submission.filename}">
                    <div class="ml-3 flex-1">
                        <div class="text-sm font-medium text-gray-900">${submission.student_name}</div>
                        <div class="text-sm text-gray-500">${submission.filename}</div>
                        <div class="text-xs text-gray-400">Status: ${submission.processing_status || 'Pending'}</div>
                    </div>
                </label>
            `;
                container.appendChild(div);
            });

            // Add event listeners to checkboxes
            container.querySelectorAll('.submission-checkbox').forEach(checkbox => {
                checkbox.addEventListener('change', () => {
                    this.updateSelectedSubmissions();
                });
            });
        }

        toggleAllSubmissions(checked) {
            document.querySelectorAll('.submission-checkbox').forEach(checkbox => {
                checkbox.checked = checked;
            });
            this.updateSelectedSubmissions();
        }

        updateSelectedSubmissions() {
            this.selectedSubmissions.clear();
            document.querySelectorAll('.submission-checkbox:checked').forEach(checkbox => {
                this.selectedSubmissions.add(checkbox.value);
            });

            document.getElementById('selectedCount').textContent = `${this.selectedSubmissions.size} selected`;

            // Update select all checkbox
            const selectAllCheckbox = document.getElementById('selectAllSubmissions');
            const allCheckboxes = document.querySelectorAll('.submission-checkbox');
            selectAllCheckbox.checked = allCheckboxes.length > 0 && this.selectedSubmissions.size === allCheckboxes.length;
        }

        async processMarkingGuide() {
            if (!this.selectedGuideId) {
                this.showError('Please select a marking guide first');
                return;
            }

            this.showModal('Processing Marking Guide', 'Extracting structured content from marking guide...');

            try {
                const response = await fetch('/api/v1/processing/guide', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': this.getCSRFToken()
                    },
                    body: JSON.stringify({
                        guide_id: this.selectedGuideId
                    })
                });

                const data = await response.json();

                if (data.success) {
                    this.hideModal();
                    this.showSuccess(`Guide processed successfully! Extracted ${data.data.questions_extracted} questions.`);
                    // Refresh guide info
                    this.selectMarkingGuide(this.selectedGuideId);
                } else {
                    this.hideModal();
                    this.showError(data.error || 'Failed to process marking guide');
                }
            } catch (error) {
                this.hideModal();
                console.error('Error processing guide:', error);
                this.showError('Failed to process marking guide');
            }
        }

        async startBatchProcessing() {
            if (!this.selectedGuideId) {
                this.showError('Please select a marking guide first');
                return;
            }

            if (this.selectedSubmissions.size === 0) {
                this.showError('Please select at least one submission');
                return;
            }

            const maxQuestions = document.getElementById('maxQuestions').value;
            const processSteps = [];

            if (document.getElementById('processGradingStep').checked) processSteps.push('grading');

            if (processSteps.length === 0) {
                this.showError('Please enable grading processing');
                return;
            }

            this.isProcessing = true;
            this.updateProcessingControls();

            this.showModal('Batch Processing', 'Starting batch processing...');

            try {
                const response = await fetch('/api/v1/processing/batch-enhanced', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': this.getCSRFToken()
                    },
                    body: JSON.stringify({
                        submission_ids: Array.from(this.selectedSubmissions),
                        marking_guide_id: this.selectedGuideId,
                        max_questions_to_answer: maxQuestions ? parseInt(maxQuestions) : null,
                        process_steps: processSteps
                    })
                });

                const data = await response.json();

                this.hideModal();

                if (data.status === 'success') {
                    this.displayBatchResults(data.data);
                    this.showSuccess(data.message || 'Batch processing completed successfully!');
                } else {
                    this.showError(data.message || data.error || 'Batch processing failed');
                }
            } catch (error) {
                this.hideModal();
                console.error('Error in batch processing:', error);
                this.showError('Batch processing failed');
            } finally {
                this.isProcessing = false;
                this.updateProcessingControls();
            }
        }

        stopProcessing() {
            this.isProcessing = false;
            this.updateProcessingControls();
            this.hideModal();
            this.showInfo('Processing stopped');
        }

        updateProcessingControls() {
            document.getElementById('startBatchProcessing').disabled = this.isProcessing;
            document.getElementById('stopProcessing').disabled = !this.isProcessing;
        }

        displayBatchResults(results) {
            // Update results summary
            const summary = results.summary;
            const summaryContainer = document.getElementById('resultsSummary');

            summaryContainer.innerHTML = `
            <div class="grid grid-cols-2 gap-4">
                <div class="bg-green-50 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-green-600">${summary.successful_grading}</div>
                    <div class="text-sm text-green-600">Successfully Graded</div>
                </div>
                <div class="bg-red-50 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-red-600">${summary.failed_grading}</div>
                    <div class="text-sm text-red-600">Failed</div>
                </div>
                <div class="bg-blue-50 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-blue-600">${summary.successful_processing}</div>
                    <div class="text-sm text-blue-600">Processed</div>
                </div>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <div class="text-2xl font-bold text-gray-600">${summary.total_submissions}</div>
                    <div class="text-sm text-gray-600">Total Submissions</div>
                </div>
            </div>
        `;

            // Update detailed results table
            this.updateResultsTable(results);
        }

        updateResultsTable(results) {
            const tbody = document.getElementById('resultsTableBody');
            tbody.innerHTML = '';

            for (const [submissionId, gradingResult] of Object.entries(results.grading_results)) {
                const submissionProcessing = results.submission_processing[submissionId];

                const row = document.createElement('tr');
                row.innerHTML = `
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Student Name</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">Submission ${submissionId.substring(0, 8)}...</td>
                <td class="px-6 py-4 whitespace-nowrap">
                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${gradingResult.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }">
                        ${gradingResult.success ? 'Completed' : 'Failed'}
                    </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${gradingResult.success ? `${gradingResult.total_score}/${gradingResult.max_possible_score}` : 'N/A'}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${gradingResult.success ? gradingResult.grade_level : 'N/A'}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${gradingResult.success ? gradingResult.questions_graded : 'N/A'}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button class="text-blue-600 hover:text-blue-900" onclick="viewDetails('${submissionId}')">View Details</button>
                </td>
            `;
                tbody.appendChild(row);
            }
        }

        showModal(title, message, progress = 0) {
            document.getElementById('modalTitle').textContent = title;
            document.getElementById('modalMessage').textContent = message;
            document.getElementById('modalProgress').style.width = `${progress}%`;
            document.getElementById('processingModal').classList.remove('hidden');
        }

        hideModal() {
            document.getElementById('processingModal').classList.add('hidden');
        }

        showAuthenticationError() {
            // Create a special notification with retry option
            const notification = document.createElement('div');
            notification.className = 'fixed top-4 right-4 p-4 rounded-md shadow-lg z-50 bg-yellow-100 text-yellow-800 border border-yellow-300';
            notification.innerHTML = `
            <div class="flex items-center justify-between">
                <div>
                    <strong>Authentication Required</strong><br>
                    Please log in again or refresh the page.
                </div>
                <div class="ml-4">
                    <button onclick="window.location.reload()" class="bg-yellow-600 text-white px-3 py-1 rounded text-sm hover:bg-yellow-700">
                        Refresh Page
                    </button>
                </div>
            </div>
        `;

            document.body.appendChild(notification);

            // Remove after 10 seconds (longer for auth errors)
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 10000);
        }

        showSuccess(message) {
            this.showNotification(message, 'success');
        }

        showError(message) {
            this.showNotification(message, 'error');
        }

        showInfo(message) {
            this.showNotification(message, 'info');
        }

        showNotification(message, type) {
            // Create notification element
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-md shadow-lg z-50 ${type === 'success' ? 'bg-green-100 text-green-800' :
                type === 'error' ? 'bg-red-100 text-red-800' :
                    'bg-blue-100 text-blue-800'
                }`;
            notification.textContent = message;

            document.body.appendChild(notification);

            // Remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 5000);
        }

        getCSRFToken() {
            // Try to get CSRF token from hidden input field first (most reliable)
            const inputToken = document.querySelector('input[name=csrf_token]')?.value;
            if (inputToken) {
                return inputToken;
            }

            // Fallback: try to get from meta tag
            const metaToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content');
            if (metaToken) {
                return metaToken;
            }

            // Last resort: log error and return empty string
            console.error('CSRF token not found in input field or meta tag');
            return '';
        }
    }

    // Initialize the enhanced processing manager
    const processingManager = new EnhancedProcessingManager();

    // Global function for viewing details
    function viewDetails(submissionId) {
        const manager = window.enhancedProcessingManager;
        if (manager && manager.batchResults && manager.batchResults.grading_results) {
            const result = manager.batchResults.grading_results[submissionId];
            if (result) {
                let detailsHtml = `
                <div class="space-y-4">
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-gray-900">Grading Summary</h4>
                        <div class="mt-2 grid grid-cols-2 gap-4 text-sm">
                            <div><span class="font-medium">Total Score:</span> ${result.total_score || 'N/A'}</div>
                            <div><span class="font-medium">Percentage:</span> ${result.percentage || 'N/A'}%</div>
                            <div><span class="font-medium">Grade Level:</span> ${result.grade_level || 'N/A'}</div>
                            <div><span class="font-medium">Questions Graded:</span> ${result.questions_graded || 'N/A'}</div>
                        </div>
                    </div>
                    ${result.error ? `<div class="bg-red-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-red-800">Error</h4>
                        <p class="text-red-700 text-sm mt-1">${result.error}</p>
                    </div>` : ''}
                    <div class="bg-blue-50 p-4 rounded-lg">
                        <h4 class="font-semibold text-blue-800">Submission ID</h4>
                        <p class="text-blue-700 text-sm mt-1 font-mono">${submissionId}</p>
                    </div>
                </div>
            `;

                manager.showModal('Grading Details', detailsHtml);
            } else {
                manager.showError('No details available for this submission');
            }
        } else {
            console.log('View details for submission:', submissionId);
            alert('Details not available. Please run batch processing first.');
        }
    }
</script>
{% endblock %}