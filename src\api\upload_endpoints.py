"""Upload API endpoints with content validation and duplicate detection.

This module provides REST API endpoints for file uploads with integrated
content validation, duplicate detection, and enhanced error handling.
"""

from flask import Blueprint, request, jsonify, session, current_app
from flask_wtf.csrf import validate_csrf
from werkzeug.utils import secure_filename
import os
from typing import Dict, Any

from src.services.enhanced_upload_service import EnhancedUploadService
from src.services.consolidated_ocr_service import ConsolidatedOCRService as OCRService
from src.database.models import db, User, MarkingGuide
from utils.logger import logger
from utils.input_sanitizer import InputSanitizer

# Create blueprint
upload_bp = Blueprint('upload', __name__, url_prefix='/api/upload')

# Initialize services
ocr_service = None
upload_service = None
input_sanitizer = InputSanitizer()

def init_upload_services(app):
    """Initialize upload services with app context.
    
    Args:
        app: Flask application instance
    """
    global ocr_service, upload_service
    
    with app.app_context():
        try:
            # Initialize OCR service
            ocr_service = OCRService()
            
            # Initialize upload service
            upload_folder = app.config.get('UPLOAD_FOLDER', 'uploads')
            upload_service = EnhancedUploadService(upload_folder, ocr_service)
            
            logger.info("Upload services initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize upload services: {e}")
            raise

def require_auth():
    """Check if user is authenticated.
    
    Returns:
        User ID if authenticated, None otherwise
    """
    user_id = session.get('user_id')
    if not user_id:
        return None
    
    # Verify user exists in database
    user = db.session.query(User).filter(User.id == user_id).first()
    return user.id if user else None

@upload_bp.route('/submission', methods=['POST'])
def upload_submission():
    """Upload a submission file with duplicate detection.
    
    Expected form data:
    - file: The uploaded file
    - marking_guide_id: ID of the marking guide
    - student_name: Name of the student
    - student_id: Student ID
    - check_duplicates: Optional, whether to check for duplicates (default: true)
    
    Returns:
        JSON response with upload result
    """
    try:
        # Check authentication
        user_id = require_auth()
        if not user_id:
            return jsonify({
                'success': False,
                'error': 'Authentication required',
                'code': 'AUTH_REQUIRED'
            }), 401
        
        # Validate CSRF tokenrm data
        try:
            csrf_token = request.headers.get('X-CSRFToken') o')
            if not csrf_token:
                raise ValueError("The CSRF token is missin")
            validate_csrf(csken)
        except Exception as e:
            logger.warning(f"CSRF validation failed: {e}")
            return jsonify({
                'sulse,
        iled',
                'code': 'CRROR'
            }), 400
        
        # Validate request
        if 'file' not in request.files:
            return jsonify({
                'su
        
                'code': 'NO_FILE'
            }), 400
        
        file = request.files['fil
        if not file or not file.filename:
            return jsonify({
                'su: False,
        ,
                'code':ELECTED'
            }), 400
        
        # Get form data
        marking_guide_id = request.form.get('marking_guide_id')
        strip()
        student_id = request.form.()
        check_duplicates = requeue'
        
        # Validate required fields
        if not marking_guide_id:
            return jsonify({
                'suFalse,
        
                'code': 'MISGUIDE_ID'
            }), 400
        
        if not student_name:
            return jsonify({
                'su
        
                'code': 'M
            }), 400
        
        if not student_id:
            return jsonify({
                'sualse,
        ed',
                'code': '_ID'
            }), 400
        
        s
        student_name = input_sanitizer.sanitize_text(stud_name)
        student_id = input_sanitizer.sanitize_text(student_id)
        
        # Verify marking guide exists and ber
        marking_guide = db.session.query(M(
            Marki
        d,
            MarkingGuide.is_a True
        ).first()
        
        if not marking_guide:
            return jsonify({
                'sulse,
        ',
                'code': 'GU
            }), 404
        
        # Upload submission
        result = upload_service.upload_submiss
            file=file,
            user_id=user_id,
            marking_guide_id=marking_guide_id,
         e,
        id,
            check_duplicates=check_duplicates
        )
        
        # Determine response status code
        if result['success']:
            status_code = 201  # Created
        elif result.get('code') == 'DUPLICATT':
            sonflict
        elif result.get('code') in ['NO_FILE', 'INVALI]:
        t
        else:
        
        
        return jsonify(result), status_code
        
    except Exception as e:
        logger.error(f"Error in upload_submis")
        return jsonify({
            'su
,
            'code': 'INTERNAL_ERROR'
        }), 500

@upl'POST'])
def upload_marking_guid
    """Upload a marking guidetion.
    
    Expected form data:
    ed file
    - title:de
    - check_duplicates: Optional, whethet: true)
    
    Retu
        JSON response with upl
    """
    try:
        # Check authentication
        user_id = require_auth()
        if not user_id:
            return jsonify({
                'suFalse,
        
                'code': 'AUTH'
            
        
        # Validate CSRF token m data
        try:
            csrf_token = req
            if not csrf_token:
                raise ValueError("The CSRF token is miss
            validate_csrf(csrf_token)
        except Excee:
         {e}")
            return jsonify
                'success': False,
                'error': 'CSed',
                'code': 'CSRF_ERR'
            }), 400
        
        # Validate 
        es:
            return jsonify({
                'success': False,
                'error': 'Noed',
                'code': 'NO_FILE'
            }), 400
        
        file = requle']
        :
            return json
                'success': False,
                'error': 'No file selected',
        ED'
            }), 400
        
        # Get form data
        title = request.form.get(p()
        check_duplicates = request.form.get('== 'true'
        
        # Validate ds
        e:
            return jsonify({
                'success': False,
        quired',
                'code': 'MISSIE'
            }), 400
        
        # Sanitize inputs
        title = input_sale)
        
        #e
        
            file=file,
            user_id=user_id,
            title=title,
            check_duplicates=check_duplicates
        )
        
        # Determine response status code
        if recess']:
            status_code = 201  # Created
        :
            status_code = 409  # Conflict
        ]:
            status_code = 
        else:
            status_code  Error
        
        return jsonify(result), status_code
        
    except Excee:
")
        return jsonify({
            'success':,
            'error': 'Internal server error',
    
        }), 500

@upload_bp.route('/check-duplicate', method
def check_duplicate():
    ng.
    
    Expected form data:
    - fk
    - tye'
    - marking_guide_id: Requir
    
    Returns:
        JSON response with dult
    """
    try:
        # Check authentication
        user_id = re_auth()
        _id:
            return jsonify
                'success': False,
                'error': 'Auired',
                'code': 'AUTH_REQ'
            }), 401
        
        # Validate request
        les:
            return jsonify({
                'success': False,
                'error': 'No
                'code': 'NO_FILE'
            }), 400
        
        file = requile']
        ename:
            return json
                'success': False,
                'error': 'No file selected',
        
            }), 400
        
        # Get form data
        check_type = request.form
        marking_guide_id = request.form.get('marking_guide_id')
        
        # Validate e
        de']:
            return jsony({
                'success': False,
                'error': 'Type must be "subm
                'code': 'INVALID_TYPE'
        400
        
        e file
        validation_result = upload_service.validate_file)
        if not validatiess']:
            return jsonify(validation_result), 400
        
        file_type = validation_result[]
        
        # Sa
        import tempfile
        with tempfile.NamedTemporaryFile(delete=False, suffix=f'.{file_type}') as tele:
            file.save(temp_file.name)
            tname
        
        try:
            # Check for duplicates
            
                temp_path, file_type, u_id
            
            
            # Clean up temp file
            os.unlink(temp_path)
            
            return 
            
        except Exception a e:
            # Clean up temp file on error
            if os.path.e
                os.unlink(tem
            raise e
        
    except Exce
")
        return jsonify({
            'success
            'error': 'Internal server error',
    
        }), 500

@upl
def validate):
    """Validate uploaded file without saving
    
    Expe data:
    - file: The file to validate
    
    Returns:
        JSON response with v
    """
    try:
        # Check authentication
        user_id = r()
        
            return jsonify
                'success': False,
                'error': 'Au
                'code': 'AUTH_REQ
            }), 401
        
        # Validate quest
        iles:
            return jsonify({
                'success': False,
                'error': 'No',
                'code': 'NO_FILE'
            }), 400
        
        file = requ
        ame:
            return jsony({
                'success': False,
        elected',
                'code': 'NO_FILE_SELECTED'
        
        
        # Validate file
        result = upload_(file)
        
        return jsonify(result), 200 if resultse 400
        
    except Exce:

        return jsonify({
            'success': False,
            'error': 'Internal server error',
    RROR'
        }), 500

@upload
def clea():
    """Clean up temporary fileonly).
    
    Returns:
        JSON response with cresult
    """
    try:
        # Check authentication
        user_id = r()
        
            return jsonify({
                'success': False,
                'error': 'Authentication required',
                'code': 'AUTRED'
            }), 401
        
        # Check if user is admin (you mahecking)
        user = db.s()
        ):
            return jsonify({
                'success': False,
        required',
                'code': 'ADMIED'
            }), 403
        
        # Get max age parameter
         24
        
        # Clean up temp files
        result = upload_s)
        
        return jsonify(result), 200
        
    except Exceon as e:

        return jsonify({
            'success': False
            'error': 'Internal server error',
    L_ERROR'
        }), 00

@upload
def get_:
    """Get list of suppo
    
    Returns:
        JSON response with supporteormats
    """
    try:
        return jsonify({
            'success': True,
            'supported_formats': list(upls),
            'max_file_size_mb': 50,
            'description': {
                'pdf': 'Portable Do
                'docx': 'Microsoft Wo',
                'doc': 'Microsoft Wor
             ile',
               
        mage',
                'png': 'PNe',
                'tiff': 'TIFF Image',
                'bmp': 'Image'
            }
        }), 200
        
    except Exce

        return jy({
            'success': False,
            'error': 'Inter',
            'code': 'INTERNAL_ERROR'
        }), 500

# Error handlers
@upload_bp.errorhandler(413)
def file_to):
""
    return jsonify({
        'success': False,
        'error': 'File size exceeds
        'code': 'FILLARGE'
    }), 413

@upload_bp.errorhandler(400)
def bad_req
"""
    return jsonify({
        'success': False,
        'error': 'Bad request',
        'code': 'BAD_REQUEST'
    }), 400

@upload_bp.errorhandler(500)
def internal_error(error):
    """Hand0    }), 50RROR'
RNAL_ENTE'I   'code': ',
     r errorrnal serve 'Interor':
        'er False,success':  '
      nify({eturn jso   rrror}")
 points: {eupload endrror in erver e sternalror(f"In logger.er""
   or."er errnal servle inter