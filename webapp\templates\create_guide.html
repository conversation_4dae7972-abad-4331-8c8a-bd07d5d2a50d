{% extends "layout.html" %}

{% block content %}
<div class="max-w-4xl mx-auto">
    <!-- <PERSON> Header -->
    <div class="mb-8">
        <h1 class="text-2xl font-bold text-gray-900">Create Marking Guide</h1>
        <p class="mt-2 text-sm text-gray-600">
            Create a new marking guide from scratch with custom questions and criteria.
        </p>
    </div>

    <form method="POST" id="create-guide-form">
        {% if csrf_token %}
        <input type="hidden" name="csrf_token" value="{{ csrf_token }}" />
        {% endif %}
        <div class="space-y-8">
            <!-- Basic Information -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Basic Information</h3>
                    
                    <!-- Guide Name -->
                    <div class="mb-6">
                        <label for="guide_name" class="block text-sm font-medium text-gray-700 mb-2">
                            Guide Name *
                        </label>
                        <input type="text" id="guide_name" name="guide_name" required
                               class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                               placeholder="e.g., Computer Science Midterm Exam">
                    </div>

                    <!-- Description -->
                    <div class="mb-6">
                        <label for="guide_description" class="block text-sm font-medium text-gray-700 mb-2">
                            Description
                        </label>
                        <textarea id="guide_description" name="guide_description" rows="3"
                                  class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                                  placeholder="Brief description of this marking guide..."></textarea>
                    </div>

                    <!-- Total Marks -->
                    <div class="mb-6">
                        <label for="total_marks" class="block text-sm font-medium text-gray-700 mb-2">
                            Total Marks *
                        </label>
                        <input type="number" id="total_marks" name="total_marks" required min="1" max="1000"
                               class="block w-32 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                               placeholder="100">
                        <p class="mt-1 text-sm text-gray-500">
                            Total marks for the entire exam/assignment
                        </p>
                    </div>
                </div>
            </div>

            <!-- Questions Section -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Questions</h3>
                        <button type="button" id="add-question" 
                                class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <svg class="mr-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                            </svg>
                            Add Question
                        </button>
                    </div>

                    <div id="questions-container" class="space-y-6">
                        <!-- Questions will be added here dynamically -->
                    </div>

                    <!-- Question Count Hidden Input -->
                    <input type="hidden" id="question_count" name="question_count" value="0">
                </div>
            </div>

            <!-- Summary -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Summary</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="text-gray-500">Total Questions</div>
                            <div id="summary-questions" class="text-2xl font-bold text-gray-900">0</div>
                        </div>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="text-gray-500">Allocated Marks</div>
                            <div id="summary-allocated" class="text-2xl font-bold text-gray-900">0</div>
                        </div>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="text-gray-500">Remaining Marks</div>
                            <div id="summary-remaining" class="text-2xl font-bold text-gray-900">0</div>
                        </div>
                    </div>

                    <div id="marks-warning" class="hidden mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"/>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-yellow-800">Mark Allocation Warning</h3>
                                <div class="mt-2 text-sm text-yellow-700">
                                    <p id="marks-warning-text">The allocated marks don't match the total marks.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-between">
                <a href="{{ url_for('marking_guides') }}" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                    </svg>
                    Back to Library
                </a>
                
                <div class="flex space-x-3">
                    <button type="button" id="preview-guide" 
                            class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                        </svg>
                        Preview
                    </button>
                    
                    <button type="submit" id="create-guide-btn"
                            class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                        </svg>
                        Create Guide
                    </button>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Question Template (Hidden) -->
<template id="question-template">
    <div class="question-item border border-gray-200 rounded-lg p-4">
        <div class="flex items-center justify-between mb-4">
            <h4 class="text-md font-medium text-gray-900">Question <span class="question-number">1</span></h4>
            <button type="button" class="remove-question text-danger-600 hover:text-danger-500">
                <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        </div>
        
        <div class="space-y-4">
            <!-- Question Text -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Question Text *
                </label>
                <textarea class="question-text block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm" 
                          rows="2" required placeholder="Enter the question text..."></textarea>
            </div>
            
            <!-- Marks -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Marks *
                </label>
                <input type="number" class="question-marks block w-24 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm" 
                       min="1" max="100" required placeholder="10">
            </div>
            
            <!-- Marking Criteria -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    Marking Criteria
                </label>
                <textarea class="question-criteria block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm" 
                          rows="2" placeholder="Describe what students need to include for full marks..."></textarea>
            </div>
        </div>
    </div>
</template>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    const questionsContainer = document.getElementById('questions-container');
    const addQuestionBtn = document.getElementById('add-question');
    const questionCountInput = document.getElementById('question_count');
    const totalMarksInput = document.getElementById('total_marks');
    const createGuideForm = document.getElementById('create-guide-form');
    
    let questionCount = 0;

    // Add question functionality
    addQuestionBtn.addEventListener('click', addQuestion);

    // Update summary when inputs change
    document.addEventListener('input', updateSummary);
    document.addEventListener('change', updateSummary);

    function addQuestion() {
        questionCount++;
        const template = document.getElementById('question-template');
        const questionElement = template.content.cloneNode(true);
        
        // Update question number
        questionElement.querySelector('.question-number').textContent = questionCount;
        
        // Set input names
        const questionItem = questionElement.querySelector('.question-item');
        questionItem.dataset.questionNumber = questionCount;
        
        questionElement.querySelector('.question-text').name = 'question_text[]';
        questionElement.querySelector('.question-marks').name = 'question_marks[]';
        questionElement.querySelector('.question-criteria').name = 'question_criteria[]';
        
        // Add remove functionality
        questionElement.querySelector('.remove-question').addEventListener('click', function() {
            removeQuestion(questionItem);
        });
        
        questionsContainer.appendChild(questionElement);
        questionCountInput.value = questionCount;
        updateSummary();
        
        // Focus on the new question text
        setTimeout(() => {
            questionItem.querySelector('.question-text').focus();
        }, 100);
    }

    function removeQuestion(questionItem) {
        questionItem.remove();
        renumberQuestions();
        updateSummary();
    }

    function renumberQuestions() {
        const questions = questionsContainer.querySelectorAll('.question-item');
        questionCount = questions.length;
        
        questions.forEach((question, index) => {
            const number = index + 1;
            question.dataset.questionNumber = number;
            question.querySelector('.question-number').textContent = number;
            question.querySelector('.question-text').name = 'question_text[]';
            question.querySelector('.question-marks').name = 'question_marks[]';
            question.querySelector('.question-criteria').name = 'question_criteria[]';
        });
        
        questionCountInput.value = questionCount;
    }

    function updateSummary() {
        const totalMarks = parseInt(totalMarksInput.value) || 0;
        const questions = questionsContainer.querySelectorAll('.question-item');
        
        let allocatedMarks = 0;
        questions.forEach(question => {
            const marks = parseInt(question.querySelector('.question-marks').value) || 0;
            allocatedMarks += marks;
        });
        
        const remainingMarks = totalMarks - allocatedMarks;
        
        document.getElementById('summary-questions').textContent = questions.length;
        document.getElementById('summary-allocated').textContent = allocatedMarks;
        document.getElementById('summary-remaining').textContent = remainingMarks;
        
        // Show warning if marks don't match
        const warningDiv = document.getElementById('marks-warning');
        const warningText = document.getElementById('marks-warning-text');
        
        if (totalMarks > 0 && allocatedMarks !== totalMarks) {
            if (allocatedMarks > totalMarks) {
                warningText.textContent = `You have allocated ${allocatedMarks - totalMarks} more marks than the total.`;
            } else {
                warningText.textContent = `You need to allocate ${totalMarks - allocatedMarks} more marks.`;
            }
            warningDiv.classList.remove('hidden');
        } else {
            warningDiv.classList.add('hidden');
        }
    }

    // Form validation
    createGuideForm.addEventListener('submit', function(e) {
        const questions = questionsContainer.querySelectorAll('.question-item');
        
        if (questions.length === 0) {
            e.preventDefault();
            alert('Please add at least one question.');
            return false;
        }
        
        const totalMarks = parseInt(totalMarksInput.value) || 0;
        let allocatedMarks = 0;
        
        questions.forEach(question => {
            const marks = parseInt(question.querySelector('.question-marks').value) || 0;
            allocatedMarks += marks;
        });
        
        if (allocatedMarks !== totalMarks) {
            e.preventDefault();
            alert('The total marks must equal the sum of individual question marks.');
            return false;
        }
    });

    // Add first question by default
    addQuestion();
});
</script>
{% endblock %}
