<!DOCTYPE html>
<html lang="en" class="h-full bg-gray-50">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Exam Grader - AI-powered educational assessment platform">
    <title>{% block title %}{{ page_title }} - Exam Grader{% endblock %}</title>

    <!-- Favicon -->
    <link rel="icon" href="{{ url_for('static', filename='favicon.ico') }}" type="image/x-icon">
    <link rel="icon" type="image/svg+xml" href="{{ url_for('static', filename='favicon.svg') }}">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>

    <!-- Security Headers -->
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
    <meta http-equiv="Referrer-Policy" content="strict-origin-when-cross-origin">

    <!-- CSRF Token for JavaScript -->
    <meta name="csrf-token" content="{{ csrf_token }}">

    <!-- Authentication styles are now included in consolidated-styles.css -->
</head>
<body class="h-full font-sans antialiased">
    <!-- Background Pattern -->
    <div class="min-h-screen bg-gradient-to-br from-primary-50 via-blue-50 to-indigo-100 relative overflow-hidden">
        <!-- Decorative background elements -->
        <div class="absolute inset-0 overflow-hidden">
            <div class="absolute -top-40 -right-40 w-80 h-80 bg-primary-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
            <div class="absolute -bottom-40 -left-40 w-80 h-80 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse animation-delay-2000"></div>
            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 bg-indigo-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse animation-delay-4000"></div>
        </div>

        <!-- Main Content -->
        <div class="relative z-10 min-h-screen flex flex-col">
            <!-- Header -->
            <header class="flex-shrink-0 py-6">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex items-center justify-between">
                        <!-- Logo and Brand -->
                        <div class="flex items-center">
                            <a href="{{ url_for('landing') }}" class="flex items-center group">
                                <div class="h-10 w-10 bg-primary-600 rounded-lg flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow">
                                    <svg class="h-6 w-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h1 class="text-xl font-bold text-gray-900 group-hover:text-primary-600 transition-colors">Exam Grader</h1>
                                    <p class="text-xs text-gray-600">AI-Powered Assessment</p>
                                </div>
                            </a>
                        </div>

                        <!-- Navigation Links -->
                        <nav class="flex items-center space-x-4">
                            {% block header_nav %}
                            <a href="{{ url_for('auth.login') }}" 
                               class="text-gray-700 hover:text-primary-600 px-3 py-2 rounded-md text-sm font-medium transition-colors
                                      {% if request.endpoint == 'auth.login' %}text-primary-600 bg-primary-50{% endif %}">
                                Sign In
                            </a>
                            <a href="{{ url_for('auth.signup') }}" 
                               class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors auth-button
                                      {% if request.endpoint == 'auth.signup' %}bg-primary-700{% endif %}">
                                Sign Up
                            </a>
                            {% endblock %}
                        </nav>
                    </div>
                </div>
            </header>

            <!-- Main Content Area -->
            <main class="flex-1 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
                <div class="max-w-md w-full space-y-8">
                    {% block content %}{% endblock %}
                </div>
            </main>

            <!-- Footer -->
            <footer class="flex-shrink-0 py-6">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="text-center">
                        <p class="text-sm text-gray-500">
                            © {{ current_year or 2025 }} Exam Grader. 
                            <span class="hidden sm:inline">AI-powered educational assessment platform.</span>
                        </p>
                        <div class="mt-2 flex justify-center space-x-4 text-xs text-gray-400">
                            <a href="#" class="hover:text-gray-600 transition-colors">Privacy Policy</a>
                            <span>•</span>
                            <a href="#" class="hover:text-gray-600 transition-colors">Terms of Service</a>
                            <span>•</span>
                            <a href="#" class="hover:text-gray-600 transition-colors">Support</a>
                        </div>
                    </div>
                </div>
            </footer>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // Add animation delays for background elements
        document.addEventListener('DOMContentLoaded', function() {
            const elements = document.querySelectorAll('.animation-delay-2000');
            elements.forEach(el => {
                el.style.animationDelay = '2s';
            });
            
            const elements4000 = document.querySelectorAll('.animation-delay-4000');
            elements4000.forEach(el => {
                el.style.animationDelay = '4s';
            });
        });

        // Enhanced form interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Add focus effects to form inputs
            const inputs = document.querySelectorAll('input[type="text"], input[type="email"], input[type="password"]');
            inputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('ring-2', 'ring-primary-500', 'ring-opacity-50');
                });
                
                input.addEventListener('blur', function() {
                    this.parentElement.classList.remove('ring-2', 'ring-primary-500', 'ring-opacity-50');
                });
            });
        });
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
