{% extends "layout.html" %}

{% block content %}
<div class="max-w-3xl mx-auto">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-2xl font-bold text-gray-900" data-i18n="upload_marking_guide_title">Upload Marking Guide</h1>
        <p class="mt-2 text-sm text-gray-600" data-i18n="upload_marking_guide_description">
            Upload your marking guide to enable automated grading. Supported formats: PDF, Word documents, and images.
        </p>
    </div>

    <!-- Upload Form -->
    <div class="bg-white shadow rounded-lg" data-upload-container>
        <div class="px-4 py-5 sm:p-6">
            <!-- Message Display Area -->
            <div class="upload-messages mb-4"></div>
            <form method="POST" enctype="multipart/form-data" id="upload-form" data-upload-form>
                {% if csrf_token %}
                <input type="hidden" name="csrf_token" value="{{ csrf_token }}" />
                {% endif %}
                <!-- File Upload Area -->
                <div class="mb-6">
                    <label for="guide_file" class="block text-sm font-medium text-gray-700 mb-2" data-i18n="marking_guide_file">
                        Marking Guide File
                    </label>
                    <div class="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md hover:border-primary-400 transition-colors duration-200" id="drop-zone">
                        <div class="space-y-1 text-center">
                            <svg class="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
                                <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <div class="flex text-sm text-gray-600">
                                <label for="guide_file" class="relative cursor-pointer bg-white rounded-md font-medium text-primary-600 hover:text-primary-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-primary-500">
                                    <span data-i18n="upload_a_file">Upload a file</span>
                                    <input id="guide_file" name="guide_file" type="file" class="sr-only" accept=".pdf,.docx,.doc,.jpg,.jpeg,.png,.tiff,.bmp,.gif" required>
                                </label>
                                <p class="pl-1" data-i18n="or_drag_and_drop">or drag and drop</p>
                            </div>
                            <p class="text-xs text-gray-500" data-i18n="file_types_hint">
                                PDF, Word documents, or images up to 16MB
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Title Input -->
                <div class="mb-6">
                    <label for="title" class="block text-sm font-medium text-gray-700 mb-2" data-i18n="marking_guide_title">
                        Title <span class="text-red-500">*</span>
                    </label>
                    <input type="text" id="title" name="title" required 
                           class="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
                           placeholder="Enter a title for your marking guide" 
                           data-i18n-placeholder="marking_guide_title_placeholder">
                    <p class="mt-1 text-xs text-gray-500" data-i18n="marking_guide_title_help">
                        Provide a descriptive title to help identify this marking guide
                    </p>
                </div>

                <!-- File Preview -->
                <div id="file-preview" class="hidden mb-6">
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                                </svg>
                            </div>
                            <div class="ml-4 flex-1">
                                <div class="text-sm font-medium text-gray-900" id="file-name"></div>
                                <div class="text-sm text-gray-500" id="file-size"></div>
                            </div>
                            <div class="ml-4">
                                <button type="button" id="remove-file" class="text-gray-400 hover:text-gray-500">
                                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Upload Progress -->
                <div id="upload-progress" class="hidden mb-6">
                    <div class="bg-gray-200 rounded-full h-2">
                        <div id="progress-bar" class="bg-primary-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    <p class="text-sm text-gray-600 mt-2" data-i18n="uploading_processing">Uploading and processing...</p>
                </div>

                <!-- Form Actions -->
                <div class="flex items-center justify-between">
                    <a href="{{ url_for('dashboard') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                        <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                        </svg>
                        <span data-i18n="back_to_dashboard">Back to Dashboard</span>
                    </a>
                    
                    <button type="submit" id="submit-btn" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed">
                        <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"/>
                        </svg>
                        <span data-i18n="upload_guide_button">Upload Guide</span>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Help Section -->
    <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
        <div class="flex">
            <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"/>
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-blue-800" data-i18n="tips_for_best_results">Tips for best results</h3>
                <div class="mt-2 text-sm text-blue-700">
                    <ul class="list-disc list-inside space-y-1">
                        <li data-i18n="tip_structure">Ensure your marking guide is clearly structured with question numbers and point values</li>
                        <li data-i18n="tip_quality">Use high-quality scans or images if uploading image files</li>
                        <li data-i18n="tip_format">PDF and Word documents typically provide the best OCR results</li>
                        <li data-i18n="tip_answers">Include sample answers or key points for each question when possible</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{{ url_for('static', filename='js/translations.js') }}"></script>
<!-- Include duplicate detection functionality -->
<script src="{{ url_for('static', filename='js/duplicate_detection.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const dropZone = document.getElementById('drop-zone');
    const fileInput = document.getElementById('guide_file');
    const filePreview = document.getElementById('file-preview');
    const fileName = document.getElementById('file-name');
    const fileSize = document.getElementById('file-size');
    const removeFileBtn = document.getElementById('remove-file');
    const uploadProgress = document.getElementById('upload-progress');
    const progressBar = document.getElementById('progress-bar');
    const submitBtn = document.getElementById('submit-btn');
    const uploadForm = document.getElementById('upload-form');

    // Drag and drop functionality
    dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        dropZone.classList.add('border-primary-500', 'bg-primary-50');
    });

    dropZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        dropZone.classList.remove('border-primary-500', 'bg-primary-50');
    });

    dropZone.addEventListener('drop', function(e) {
        e.preventDefault();
        dropZone.classList.remove('border-primary-500', 'bg-primary-50');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            handleFileSelect(files[0]);
        }
    });

    // File input change
    fileInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            handleFileSelect(e.target.files[0]);
        }
    });

    // Remove file
    removeFileBtn.addEventListener('click', function() {
        fileInput.value = '';
        filePreview.classList.add('hidden');
        dropZone.classList.remove('hidden');
    });

    // Handle file selection
    function handleFileSelect(file) {
        fileName.textContent = file.name;
        fileSize.textContent = formatFileSize(file.size);
        filePreview.classList.remove('hidden');
        dropZone.classList.add('hidden');
    }

    // Format file size
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Form submission with progress
    uploadForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (!fileInput.files.length) {
            alert(ExamGrader.translate('please_select_file'));
            return;
        }

        const formData = new FormData(uploadForm);
        
        // Debug: Log form data
        console.log('Form data being submitted:');
        for (let [key, value] of formData.entries()) {
            console.log(key, value);
        }
        
        // Add CSRF token to form data
        const csrfToken = document.querySelector('meta[name="csrf-token"]').content;
        formData.append('csrf_token', csrfToken);
        
        // Validate required fields before submission
        const titleField = document.getElementById('title');
        if (!titleField.value.trim()) {
            alert('Please enter a title for your marking guide.');
            uploadProgress.classList.add('hidden');
            submitBtn.disabled = false;
            ExamGrader.utils.hideButtonLoading(submitBtn, ExamGrader.translate('upload_guide_button'));
            return;
        }

        // Show progress
        uploadProgress.classList.remove('hidden');
        ExamGrader.utils.showButtonLoading(submitBtn, ExamGrader.translate('processing'));
        submitBtn.disabled = true;

        // Simulate progress
        let progress = 0;
        const progressInterval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 90) progress = 90;
            progressBar.style.width = progress + '%';
        }, 200);

        // Submit form
        fetch(uploadForm.action, {
            method: 'POST',
            body: formData
        })
        .then(async response => {
            clearInterval(progressInterval);
            progressBar.style.width = '100%';
            
            if (response.ok) {
                // Check if response is a redirect
                if (response.redirected) {
                    window.location.href = response.url;
                } else {
                    // Handle JSON response
                    try {
                        const result = await response.json();
                        if (result.success) {
                            window.location.href = result.redirect_url || '{{ url_for("dashboard") }}';
                        } else {
                            throw new Error(result.error || 'Upload failed');
                        }
                    } catch (e) {
                        // If not JSON, assume success and redirect
                        window.location.href = '{{ url_for("dashboard") }}';
                    }
                }
            } else {
                // Handle HTTP errors
                let errorMessage = 'Upload failed';
                try {
                    const errorData = await response.text();
                    // Try to extract error message from HTML response
                    const parser = new DOMParser();
                    const doc = parser.parseFromString(errorData, 'text/html');
                    const flashMessages = doc.querySelectorAll('.flash-message');
                    if (flashMessages.length > 0) {
                        errorMessage = flashMessages[0].textContent.trim();
                    }
                } catch (e) {
                    console.error('Error parsing response:', e);
                }
                throw new Error(errorMessage);
            }
        })
        .catch(error => {
            clearInterval(progressInterval);
            uploadProgress.classList.add('hidden');
            submitBtn.disabled = false;
            ExamGrader.utils.hideButtonLoading(submitBtn, ExamGrader.translate('upload_guide_button'));
            
            // Show more specific error message
            const errorMsg = error.message || ExamGrader.translate('upload_failed');
            alert(errorMsg);
            console.error('Upload error:', error);
        });
    });

    // Initialize duplicate detection
    if (window.DuplicateDetectionHandler) {
        const duplicateHandler = new DuplicateDetectionHandler({
            uploadType: 'marking_guide',
            apiEndpoints: {
                checkDuplicate: '/api/upload/check-duplicate',
                validateFile: '/api/upload/validate-file'
            }
        });
        duplicateHandler.init();
    }
});
</script>
{% endblock %}
