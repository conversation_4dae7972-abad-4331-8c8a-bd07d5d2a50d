{% extends "layout.html" %}

{% block extra_css %}
{% if session.guide_id %}
<meta name="guide-id" content="{{ session.guide_id }}">
{% endif %}
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
  <!-- Page Header -->
  <div class="mb-8">
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Submissions</h1>
        <p class="mt-2 text-sm text-gray-600">
          Manage and review all uploaded student submissions.
        </p>
      </div>
      <div class="flex space-x-3">
        <button
          onclick="clearCache()"
          class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
          id="clear-cache-btn"
        >
          <svg
            class="mr-2 h-4 w-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"
            />
          </svg>
          Clear Cache
        </button>
        <a
           href="{{ url_for('reprocess_submissions') }}"
           class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md shadow-sm text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
           title="Reprocess submissions with missing text content"
         >
          <svg
            class="mr-2 h-4 w-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
            />
          </svg>
          Reprocess
        </a>
        <div class="flex items-center text-xs text-gray-500 bg-gray-50 px-3 py-2 rounded-md">
          <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
          </svg>
          Cache: <span id="cache-entries">Loading...</span>
        </div>
        <a
          href="{{ url_for('upload_submission') }}"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
        >
          <svg
            class="mr-2 h-4 w-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 6v6m0 0v6m0-6h6m-6 0H6"
            />
          </svg>
          Upload New Submission
        </a>
      </div>
    </div>
  </div>

  <!-- Statistics Cards -->
  <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div
              class="w-8 h-8 bg-primary-500 rounded-md flex items-center justify-center"
            >
              <svg
                class="w-5 h-5 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">
                Total Submissions
              </dt>
              <dd class="text-lg font-medium text-gray-900" id="total-submissions-count">
                {{ submissions|length }}
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div
              class="w-8 h-8 bg-success-500 rounded-md flex items-center justify-center"
            >
              <svg
                class="w-5 h-5 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">
                Processed
              </dt>
              <dd class="text-lg font-medium text-gray-900" id="processed-submissions-count">
                {{ submissions|selectattr('processed')|list|length }}
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>

    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div
              class="w-8 h-8 bg-warning-500 rounded-md flex items-center justify-center"
            >
              <svg
                class="w-5 h-5 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
          </div>
          <div class="ml-5 w-0 flex-1">
            <dl>
              <dt class="text-sm font-medium text-gray-500 truncate">
                Pending
              </dt>
              <dd class="text-lg font-medium text-gray-900" id="pending-submissions-count">
                {{ submissions|rejectattr('processed')|list|length }}
              </dd>
            </dl>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Submissions Table -->
  {% if submissions %}
  <div class="bg-white shadow rounded-lg overflow-hidden">
    <div class="px-4 py-5 sm:p-6">
      <div class="flex items-center justify-between mb-4">
        <h3 class="text-lg leading-6 font-medium text-gray-900">
          All Submissions
        </h3>
        <div class="flex items-center space-x-2">
          <div class="relative">
            <input
              type="text"
              id="search-input"
              placeholder="Search submissions..."
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            />
            <div
              class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
            >
              <svg
                class="h-5 w-5 text-gray-400"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>

      <div class="overflow-x-auto">
        <table
          class="min-w-full divide-y divide-gray-200"
          id="submissions-table"
        >
          <thead class="bg-gray-50">
            <tr>
              <th
                scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Filename
              </th>
              <th
                scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Status
              </th>
              <th
                scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Uploaded
              </th>
              <th
                scope="col"
                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
              >
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            {% for submission in submissions %}
            <tr
              class="hover:bg-gray-50"
              id="submission-row-{{ submission.id }}"
            >
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <div
                      class="h-10 w-10 rounded-lg bg-gray-100 flex items-center justify-center"
                    >
                      <svg
                        class="h-6 w-6 text-gray-400"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        />
                      </svg>
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">
                      {{ submission.filename }}
                    </div>
                    <div class="text-sm text-gray-500">
                      ID: {{ submission.id[:8] }}...
                    </div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                {% if submission.processed %}
                <span
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 text-success-800"
                >
                  <svg
                    class="mr-1.5 h-2 w-2 text-success-400"
                    fill="currentColor"
                    viewBox="0 0 8 8"
                  >
                    <circle cx="4" cy="4" r="3" />
                  </svg>
                  Processed
                </span>
                {% else %}
                <span
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-warning-100 text-warning-800"
                >
                  <svg
                    class="mr-1.5 h-2 w-2 text-warning-400"
                    fill="currentColor"
                    viewBox="0 0 8 8"
                  >
                    <circle cx="4" cy="4" r="3" />
                  </svg>
                  Pending
                </span>
                {% endif %}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ submission.created_at.strftime("%Y-%m-%d") if submission.created_at else
                'Unknown' }}
              </td>
              <td
                class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium"
              >
                <div class="flex items-center space-x-2">
                  <a
                    href="{{ url_for('view_submission_content', submission_id=submission.id) }}"
                    class="text-primary-600 hover:text-primary-900"
                  >
                    View Content
                  </a>
                  <button
                    type="button"
                    class="text-info-600 hover:text-info-900"
                    onclick="downloadSubmission('{{ submission.id }}')"
                  >
                    Download
                  </button>
                  {% if submission.processed %}
                  <button
                    type="button"
                    class="text-success-600 hover:text-success-900"
                    onclick="gradeSubmission('{{ submission.id }}')"
                    id="grade-button-{{ submission.id }}"
                  >
                    Grade
                  </button>
                  {% endif %}
                  <button
                    type="button"
                    class="text-danger-600 hover:text-danger-900"
                    onclick="deleteSubmission('{{ submission.id }}')"
                    id="delete-button-{{ submission.id }}"
                  >
                    Delete
                  </button>
                </div>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>
  {% else %}
  <!-- Empty State -->
  <div class="text-center py-12">
    <svg
      class="mx-auto h-12 w-12 text-gray-400"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
      />
    </svg>
    <h3 class="mt-2 text-sm font-medium text-gray-900">No submissions</h3>
    <p class="mt-1 text-sm text-gray-500">
      Get started by uploading your first student submission.
    </p>
    <div class="mt-6">
      <a
        href="{{ url_for('upload_submission') }}"
        class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
      >
        <svg
          class="mr-2 h-4 w-4"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="2"
            d="M12 6v6m0 0v6m0-6h6m-6 0H6"
          />
        </svg>
        Upload Submission
      </a>
    </div>
  </div>
  {% endif %}
</div>

<!-- View Submission Modal -->
<div
  id="view-modal"
  class="fixed inset-0 z-50 overflow-y-auto hidden"
  aria-labelledby="modal-title"
  role="dialog"
  aria-modal="true"
>
  <div
    class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"
  >
    <div
      class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
      aria-hidden="true"
    ></div>
    <span
      class="hidden sm:inline-block sm:align-middle sm:h-screen"
      aria-hidden="true"
      >&#8203;</span
    >
    <div
      class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
    >
      <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
        <div class="sm:flex sm:items-start">
          <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
            <h3
              class="text-lg leading-6 font-medium text-gray-900"
              id="modal-title"
            >
              Submission Details
            </h3>
            <div class="mt-2">
              <div id="modal-content" class="text-sm text-gray-500">
                <!-- Content will be loaded here -->
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
        <button
          type="button"
          class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
          onclick="closeModal()"
        >
          Close
        </button>
      </div>
    </div>
  </div>
</div>
{% endblock %} {% block extra_js %}
<script>
  document.addEventListener("DOMContentLoaded", function () {
    // Search functionality
    const searchInput = document.getElementById("search-input");
    const table = document.getElementById("submissions-table");

    if (searchInput && table) {
      searchInput.addEventListener("input", function () {
        const searchTerm = this.value.toLowerCase();
        const rows = table.querySelectorAll("tbody tr");

        rows.forEach((row) => {
          const filename = row
            .querySelector("td:first-child .text-sm.font-medium")
            .textContent.toLowerCase();
          if (filename.includes(searchTerm)) {
            row.style.display = "";
          } else {
            row.style.display = "none";
          }
        });
      });
    }

    // Load cache stats on page load
    loadCacheStats();
  });

  function loadCacheStats() {
    fetch("/api/cache/stats")
      .then((response) => response.json())
      .then((data) => {
        if (data.status === "ok" && data.cache_stats) {
          const totalEntries = data.cache_stats.total_entries || 0;
          const cacheEntriesElement = document.getElementById("cache-entries");
          if (cacheEntriesElement) {
            cacheEntriesElement.textContent = `${totalEntries} entries`;
          }
        }
      })
      .catch((error) => {
        console.error("Error loading cache stats:", error);
        const cacheEntriesElement = document.getElementById("cache-entries");
        if (cacheEntriesElement) {
          cacheEntriesElement.textContent = "Error";
        }
      });
  }

  function viewSubmission(submissionId) {
    // Get submission details from the server
    const modalContent = document.getElementById("modal-content");

    // Show loading state
    modalContent.innerHTML = `
        <div class="flex justify-center items-center py-4">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            <span class="ml-2">Loading submission details...</span>
        </div>
    `;

    // Fetch submission details
    fetch(`/view-submission/${submissionId}`)
      .then((response) => {
        if (!response.ok) {
          throw new Error("Failed to load submission details");
        }
        return response.text();
      })
      .then((html) => {
        // Replace modal content with the fetched HTML
        modalContent.innerHTML = html;
      })
      .catch((error) => {
        // Show error message
        modalContent.innerHTML = `
                <div class="space-y-3">
                    <div class="text-red-500">
                        <p>Error loading submission details: ${error.message}</p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700">Submission ID</label>
                        <p class="mt-1 text-sm text-gray-900">${submissionId}</p>
                    </div>
                </div>
            `;
      });

    document.getElementById("view-modal").classList.remove("hidden");
  }

  function closeModal() {
    document.getElementById("view-modal").classList.add("hidden");
  }

  function gradeSubmission(submissionId) {
    if (
      confirm(
        "Are you sure you want to grade this submission? This will generate results based on the marking guide."
      )
    ) {
      const gradeButton = document.querySelector(
        "#grade-button-" + submissionId
      );
      ExamGrader.utils.showButtonLoading(gradeButton, "Grading...");
      
      ExamGrader.utils
        .apiRequest(
          "/api/process-ai-grading",
          {
            method: "POST",
            body: { 
              submission_ids: [submissionId]
            }
          }
        )
        .then((data) => {
          ExamGrader.utils.hideButtonLoading(gradeButton, "Grade");
          if (data.success) {
            ExamGrader.notificationManager.notify(
              "Submission graded successfully!",
              "success"
            );
            // Reload the page to update all counts, including dashboard
            window.location.reload();
          } else {
            ExamGrader.notificationManager.notify(
              data.error || data.message || "Error grading submission.",
              "error"
            );
          }
        })
        .catch((error) => {
          ExamGrader.utils.hideButtonLoading(gradeButton, "Grade");
          ExamGrader.notificationManager.notify(
            "An error occurred: " + error.message,
            "error"
          );
        });
    }
  }

  function deleteSubmission(submissionId) {
    console.log('deleteSubmission function in submissions.html called for ID:', submissionId);
    if (
      confirm(
        "Are you sure you want to delete this submission? This action cannot be undone."
      )
    ) {
      const deleteButton = document.querySelector(
        "#delete-button-" + submissionId
      );
      ExamGrader.utils.showButtonLoading(deleteButton, "Deleting...");
            ExamGrader.utils
        .apiRequest(
          "/api/delete-submission",
          {
            method: "POST",
            body: { submission_id: submissionId },
          }
        )
        .then((data) => {
          ExamGrader.utils.hideButtonLoading(deleteButton, "Delete");
          if (data.success) {
            ExamGrader.notificationManager.notify('Submission deleted successfully!', 'success');
            // Reload the page to update all counts, including dashboard
            window.location.reload();
          } else {
ExamGrader.notificationManager.notify(data.error || 'An unknown error occurred.', 'error');
          }
        })
        .catch((error) => {
          ExamGrader.utils.hideButtonLoading(deleteButton, "Delete");
          ExamGrader.notificationManager.notify(
            "An error occurred: " + error.message,
            "error"
          );
        });
    }
  }

  function clearCache() {
    if (
      confirm(
        "Are you sure you want to clear the cache? This will remove cached submission data and may require re-processing."
      )
    ) {
      const clearButton = document.getElementById("clear-cache-btn");

      // Show loading state
      if (clearButton) {
        clearButton.innerHTML = `
          <svg class="animate-spin mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          Clearing...
        `;
        clearButton.disabled = true;
      }

      fetch("/api/clear-cache", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      })
        .then((response) => response.json())
        .then((data) => {
          if (data.success) {
            ExamGrader.notificationManager.notify(
              "Cache cleared successfully!",
              "success"
            );
            // Refresh cache stats
            loadCacheStats();
          } else {
            ExamGrader.notificationManager.notify(
              "Error clearing cache: " + (data.message || "Unknown error"),
              "error"
            );
          }
        })
        .catch((error) => {
          console.error("Error:", error);
          ExamGrader.notificationManager.notify(
            "Error clearing cache: " + error.message,
            "error"
          );
        })
        .finally(() => {
          // Reset button state
          if (clearButton) {
            clearButton.innerHTML = `
              <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
              </svg>
              Clear Cache
            `;
            clearButton.disabled = false;
          }
        });
    }
  }
</script>
{% endblock %}
