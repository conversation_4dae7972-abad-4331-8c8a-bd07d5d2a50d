{% extends "layout.html" %}

{% block extra_css %}
<!-- Marking guides styles are now included in consolidated-styles.css -->
{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto">
    <!-- Page Header -->
    <div class="mb-8">
        <div class="flex items-center justify-between">
            <div>
                <h1 class="text-2xl font-bold text-gray-900">Marking Guide Library</h1>
                <p class="mt-2 text-sm text-gray-600">
                    Create, manage, and reuse your marking guides for consistent grading.
                </p>
            </div>
            <div class="flex space-x-3">
                <a href="{{ url_for('upload_guide') }}"
                    class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    Upload Guide
                </a>
                <a href="{{ url_for('create_guide') }}"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Create New Guide
                </a>
            </div>
        </div>
    </div>

    <!-- Current Guide Status -->
    {% if current_guide %}
    <div class="current-guide-section mb-6 bg-green-50 border border-green-200 rounded-lg p-4">
        <div class="flex items-start justify-between">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd"
                            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                            clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-green-800">Current Active Guide</h3>
                    <div class="mt-2 text-sm text-green-700">
                        <p><strong>{{ current_guide.name }}</strong> is currently being used for grading.</p>
                    </div>
                </div>
            </div>
            <div class="flex-shrink-0">
                <a href="{{ url_for('clear_session_guide') }}"
                    onclick="return confirm('Are you sure you want to clear the current active guide? This will remove it from your session.')"
                    class="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                    title="Clear current active guide from session">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    Clear
                </a>
            </div>
        </div>
    </div>
    {% endif %}

    <!-- Saved Guides -->
    {% if saved_guides %}
    <div class="bg-white shadow rounded-lg overflow-hidden">
        <div class="px-4 py-5 sm:p-6">
            <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Saved Marking Guides</h3>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" style="max-width: none;">
                <style>
                    /* Ensure marking guide cards have consistent width and don't get too wide */
                    .guide-card {
                        max-width: 350px !important;
                        width: 100% !important;
                        margin: 0 !important;
                    }

                    /* Responsive grid adjustments */
                    @media (min-width: 1280px) {
                        .guide-card {
                            max-width: 320px !important;
                        }
                    }

                    @media (min-width: 1536px) {
                        .guide-card {
                            max-width: 300px !important;
                        }
                    }

                    /* Ensure text truncation works properly */
                    .guide-card .truncate {
                        overflow: hidden !important;
                        text-overflow: ellipsis !important;
                        white-space: nowrap !important;
                    }

                    /* Button layout improvements */
                    .guide-card .button-grid {
                        display: grid !important;
                        grid-template-columns: 1fr 1fr !important;
                        gap: 0.5rem !important;
                    }

                    .guide-card .button-grid>* {
                        min-width: 0 !important;
                    }
                </style>
                {% for guide in saved_guides %}
                <div class="guide-card bg-white rounded-xl p-6 border border-gray-200 shadow-sm overflow-hidden"
                    data-guide-id="{{ guide.id }}">
                    <div class="flex items-start justify-between mb-4">
                        <div class="flex-1 min-w-0 pr-4">
                            <h4 class="text-lg font-semibold text-gray-900 mb-2 truncate">{{ guide.title or guide.name
                                }}</h4>
                            {% if guide.description %}
                            <p class="text-sm text-gray-600 mb-3 line-clamp-2 break-words">{{ guide.description }}</p>
                            {% endif %}

                            <div class="space-y-2 text-sm text-gray-500">
                                <div class="flex items-center">
                                    <svg class="h-4 w-4 mr-2 text-blue-500 flex-shrink-0" fill="none"
                                        stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                    </svg>
                                    <span class="truncate">{{ guide.questions|length }} questions</span>
                                </div>
                                <div class="flex items-center">
                                    <svg class="h-4 w-4 mr-2 text-yellow-500 flex-shrink-0" fill="none"
                                        stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                                    </svg>
                                    <span class="truncate">{{ guide.total_marks }} total marks</span>
                                </div>
                                <div class="flex items-center">
                                    <svg class="h-4 w-4 mr-2 text-gray-400 flex-shrink-0" fill="none"
                                        stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                            d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span class="truncate">Created {{ guide.created_at[:10] }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- Current Guide Indicator -->
                        {% if current_guide == (guide.title or guide.name) %}
                        <div class="ml-3">
                            <span
                                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd"
                                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                        clip-rule="evenodd" />
                                </svg>
                                Active
                            </span>
                        </div>
                        {% endif %}
                    </div>

                    <!-- Actions -->
                    <div class="guide-actions mt-4">
                        <!-- Primary Action Row -->
                        <div class="button-grid mb-2">
                            {% if guide.id and guide.id|string|length > 0 and guide.id != 'session_guide' and guide.id
                            != '' and guide.id != 'None' and current_guide != (guide.title or guide.name) %}
                            <a href="{{ url_for('use_guide', guide_id=guide.id) }}"
                                class="flex-1 inline-flex justify-center items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 5l7 7-7 7" />
                                </svg>
                                <span class="truncate">Use Guide</span>
                            </a>
                            {% else %}
                            <div
                                class="flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-lg text-gray-500 bg-gray-100 cursor-not-allowed">
                                <span class="truncate">{% if guide.id == 'session_guide' %}Active Guide{% else %}In
                                    Use{% endif %}</span>
                            </div>
                            {% endif %}

                            {% if guide.id and guide.id|string|length > 0 and guide.id != 'session_guide' and guide.id
                            != '' and guide.id != 'None' %}
                            <a href="{{ url_for('view_guide_content', guide_id=guide.id) }}"
                                class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <span class="truncate">Details</span>
                            </a>
                            {% endif %}
                        </div>

                        <!-- Secondary Action Row -->
                        <div class="button-grid">
                            <button type="button" onclick="viewGuide('{{ guide.id }}')"
                                class="flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                </svg>
                                <span class="truncate">Quick View</span>
                            </button>

                            <button type="button"
                                onclick="confirmDeleteGuide('{{ guide.id }}', '{{ guide.title or guide.name }}')"
                                class="inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm leading-4 font-medium rounded-lg text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                                <svg class="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor"
                                    viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                        d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                                <span class="truncate">Delete</span>
                            </button>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    {% else %}
    <!-- Empty State -->
    <div class="text-center py-12">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <h3 class="mt-2 text-sm font-medium text-gray-900">No marking guides</h3>
        <p class="mt-1 text-sm text-gray-500">Get started by creating your first marking guide or uploading an existing
            one.</p>
        <div class="mt-6 flex justify-center space-x-3">
            <a href="{{ url_for('create_guide') }}"
                class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Create New Guide
            </a>
            <a href="{{ url_for('upload_guide') }}"
                class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                Upload Guide
            </a>
        </div>
    </div>
    {% endif %}
</div>

<!-- Enhanced View Guide Modal -->
<div id="view-guide-modal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog"
    aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity backdrop-blur-sm" aria-hidden="true">
        </div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div
            class="inline-block align-bottom bg-white rounded-xl text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
            <!-- Modal Header -->
            <div class="bg-gradient-to-r from-primary-50 to-blue-50 px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-xl font-semibold text-gray-900" id="modal-title">
                        <svg class="inline w-6 h-6 mr-2 text-primary-600" fill="none" stroke="currentColor"
                            viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        Marking Guide Details
                    </h3>
                    <button type="button" onclick="closeModal()"
                        class="text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-primary-500 rounded-lg p-1">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Modal Body -->
            <div class="bg-white px-6 py-6 max-h-[70vh] overflow-y-auto custom-scrollbar">
                <div id="guide-details" class="text-sm text-gray-500">
                    <!-- Guide details will be loaded here -->
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="bg-gray-50 px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                <button type="button" onclick="closeModal()"
                    class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="delete-confirmation-modal" class="fixed inset-0 z-50 overflow-y-auto hidden"
    aria-labelledby="delete-modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity modal-backdrop" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div
            class="inline-block align-bottom bg-white rounded-xl text-left overflow-hidden shadow-2xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <!-- Modal Header -->
            <div class="bg-red-50 px-6 py-4 border-b border-red-200">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-lg font-semibold text-red-900" id="delete-modal-title">
                            Confirm Deletion
                        </h3>
                    </div>
                </div>
            </div>

            <!-- Modal Body -->
            <div class="bg-white px-6 py-4">
                <div class="text-sm text-gray-700">
                    <p class="mb-3">Are you sure you want to delete <strong id="delete-guide-name"></strong>?</p>
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mb-4">
                        <div class="flex">
                            <svg class="h-5 w-5 text-yellow-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd"
                                    d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                                    clip-rule="evenodd" />
                            </svg>
                            <div class="text-sm">
                                <p class="font-medium text-yellow-800">This action cannot be undone!</p>
                                <p class="text-yellow-700 mt-1">This will permanently remove:</p>
                                <ul class="list-disc list-inside text-yellow-700 mt-1 space-y-1">
                                    <li>All grading criteria and questions</li>
                                    <li>Mark allocations and rubrics</li>
                                    <li>Historical references to this guide</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="bg-gray-50 px-6 py-4 border-t border-gray-200 flex justify-end space-x-3">
                <button type="button" onclick="closeDeleteModal()"
                    class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M6 18L18 6M6 6l12 12" />
                    </svg>
                    Cancel
                </button>
                <button type="button" id="confirm-delete-btn" onclick="executeDelete()"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-lg text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    <span id="delete-btn-text">Delete Guide</span>
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Toast Container -->
<div id="toast-container"></div>
{% endblock %}

{% block extra_js %}
<!-- Embed guides data safely -->
<script type="application/json" id="guides-data">{{ saved_guides | tojson }}</script>

<script>
    // Store guides data for JavaScript access with safe JSON parsing
    let guidesData = [];
    try {
        const guidesDataElement = document.getElementById('guides-data');
        if (guidesDataElement && guidesDataElement.textContent) {
            guidesData = JSON.parse(guidesDataElement.textContent);
        }
    } catch (error) {
        console.error('Error parsing guides data:', error);
        // Fallback to empty array
        guidesData = [];
    }

    // Global variables for delete functionality - initialize at top level
    var currentDeleteGuideId = null;
    var currentDeleteGuideName = null;

    // Toast notification system
    function showToast(message, type = 'success') {
        // Use the ExamGrader notification manager if available
        if (typeof ExamGrader !== 'undefined' && ExamGrader.notificationManager) {
            ExamGrader.notificationManager.notify(message, type);
            return;
        }

        // Fallback to original implementation if ExamGrader is not available
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.innerHTML = `
        <div class="flex items-center">
            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                ${type === 'success' ?
                '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>' :
                '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>'
            }
            </svg>
            <span>${message}</span>
        </div>
    `;

        document.getElementById('toast-container').appendChild(toast);

        // Show toast
        setTimeout(() => toast.classList.add('show'), 100);

        // Hide and remove toast
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => toast.remove(), 300);
        }, 4000);
    }

    // View guide functionality
    function viewGuide(guideId) {
        const guide = guidesData.find(g => g.id === guideId);
        if (!guide) {
            document.getElementById('guide-details').innerHTML = `
            <div class="text-center py-8 text-red-600">
                <svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                </svg>
                <h3 class="mt-4 text-lg font-medium">Guide Not Found</h3>
                <p class="mt-2 text-sm text-gray-600">The requested marking guide could not be found in your library.</p>
            </div>`;
            return;
        }

        // Create guide details HTML
        const guideHTML = `
        <div class="space-y-4">
            <div>
                <h4 class="font-medium text-gray-900">${guide.title || guide.name}</h4>
                ${guide.description ? `<p class="text-gray-600 mt-1">${guide.description}</p>` : ''}
            </div>

            <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                    <span class="font-medium">Total Questions:</span> ${guide.questions.length}
                </div>
                <div>
                    <span class="font-medium">Total Marks:</span> ${guide.total_marks}
                </div>
                <div>
                    <span class="font-medium">Created:</span> ${guide.created_at.split('T')[0]}
                </div>
                <div>
                    <span class="font-medium">Created By:</span> ${guide.created_by}
                </div>
            </div>

            <div>
                <h5 class="font-medium text-gray-900 mb-2">Questions:</h5>
                <div class="space-y-3">
                    ${guide.questions.map(q => `
                        <div class="border border-gray-200 rounded p-3">
                            <div class="flex justify-between items-start mb-2">
                                <span class="font-medium">Question ${q.number}</span>
                                <span class="text-sm text-gray-500">${q.marks} marks</span>
                            </div>
                            <p class="text-gray-700 text-sm">${q.text}</p>
                            ${q.criteria ? `<p class="text-gray-600 text-xs mt-1"><strong>Criteria:</strong> ${q.criteria}</p>` : ''}
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
    `;

        document.getElementById('guide-details').innerHTML = guideHTML;
        document.getElementById('view-guide-modal').classList.remove('hidden');
    }

    // Delete guide functionality
    function confirmDeleteGuide(guideId, guideName) {
        // Ensure variables are properly initialized
        if (typeof currentDeleteGuideId === 'undefined') {
            window.currentDeleteGuideId = null;
        }
        if (typeof currentDeleteGuideName === 'undefined') {
            window.currentDeleteGuideName = null;
        }

        currentDeleteGuideId = guideId;
        currentDeleteGuideName = guideName;

        document.getElementById('delete-guide-name').textContent = guideName;
        document.getElementById('delete-confirmation-modal').classList.remove('hidden');
    }

    async function executeDelete() {
        // Ensure variables are accessible
        const guideId = currentDeleteGuideId || window.currentDeleteGuideId;
        if (!guideId) {
            console.error('No guide ID available for deletion');
            return;
        }

        const deleteBtn = document.getElementById('confirm-delete-btn');
        const deleteBtnText = document.getElementById('delete-btn-text');

        // Show loading state
        deleteBtn.classList.add('btn-loading');
        deleteBtnText.textContent = 'Deleting...';
        deleteBtn.disabled = true;

        try {
            // Get CSRF token from multiple sources
            const csrfToken = document.querySelector('meta[name=csrf-token]')?.getAttribute('content') ||
                document.querySelector('input[name=csrf_token]')?.value ||
                '{{ csrf_token }}';

            console.log('Delete request details:', {
                guide_id: guideId,
                csrf_token: csrfToken ? csrfToken.substring(0, 10) + '...' : 'none'
            });

            const headers = {
                'Content-Type': 'application/json',
            };

            // Add CSRF token if available
            if (csrfToken) {
                headers['X-CSRFToken'] = csrfToken;
            }

            const response = await fetch('/api/delete-guide', {
                method: 'POST',
                headers: headers,
                body: JSON.stringify({
                    guide_id: guideId
                })
            });

            console.log('Delete response status:', response.status);

            const result = await response.json();
            console.log('Delete response data:', result);

            if (result.success) {
                // Remove guide card from UI with animation
                const guideCard = document.querySelector(`[data-guide-id="${guideId}"]`);
                if (guideCard) {
                    guideCard.classList.add('fade-out');
                    setTimeout(() => {
                        guideCard.remove();

                        // Check if no guides left and show empty state
                        const remainingCards = document.querySelectorAll('.guide-card');
                        setTimeout(function () {
                            location.reload();
                        }, 500); // Add a 500ms delay // Always reload to ensure dashboard updates
                    }, 300);
                }

                // If this was the active guide, hide the current guide section
                if (result.was_active_guide) {
                    const currentGuideSection = document.querySelector('.current-guide-section');
                    if (currentGuideSection) {
                        currentGuideSection.style.display = 'none';
                        console.log('Hidden current guide section - active guide was deleted');
                    }

                    // Show a notification that the active guide was cleared
                    showToast('Active guide cleared - please select a new guide for grading', 'warning');
                }

                // Close modal and show success message
                closeDeleteModal();
                showToast(result.message, 'success');

            } else {
                throw new Error(result.error || 'Failed to delete guide');
            }

        } catch (error) {
            console.error('Delete error:', error);
            showToast(`Error deleting guide: ${error.message}`, 'error');
        } finally {
            // Reset button state
            deleteBtn.classList.remove('btn-loading');
            deleteBtnText.textContent = 'Delete Guide';
            deleteBtn.disabled = false;
        }
    }

    // Modal control functions
    function closeModal() {
        document.getElementById('view-guide-modal').classList.add('hidden');
    }

    function closeDeleteModal() {
        document.getElementById('delete-confirmation-modal').classList.add('hidden');
        // Reset variables safely
        if (typeof currentDeleteGuideId !== 'undefined') {
            currentDeleteGuideId = null;
        }
        if (typeof currentDeleteGuideName !== 'undefined') {
            currentDeleteGuideName = null;
        }
        // Also reset window variables as fallback
        window.currentDeleteGuideId = null;
        window.currentDeleteGuideName = null;
    }

    // Close modals when clicking outside
    document.addEventListener('click', function (event) {
        const viewModal = document.getElementById('view-guide-modal');
        const deleteModal = document.getElementById('delete-confirmation-modal');

        if (event.target === viewModal) {
            closeModal();
        }
        if (event.target === deleteModal) {
            closeDeleteModal();
        }
    });

    // Keyboard navigation
    document.addEventListener('keydown', function (event) {
        if (event.key === 'Escape') {
            closeModal();
            closeDeleteModal();
        }
    });
</script>
{% endblock %}