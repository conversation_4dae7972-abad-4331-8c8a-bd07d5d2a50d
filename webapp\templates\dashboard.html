{% extends "layout.html" %}

{% block extra_css %}
<!-- Dashboard styles are now included in consolidated-styles.css -->
{% endblock %}

{% block content %}
<div class="dashboard-container"
  style="max-width: 80rem; margin-left: auto; margin-right: auto; padding: 2rem 1rem; width: 100%; box-sizing: border-box;">
  <!-- Dashboard Statistics Cards -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 dashboard-grid"
    style="display: grid; grid-template-columns: repeat(1, minmax(0, 1fr)); gap: 1.5rem; margin-bottom: 2rem;">
    <style>
      @media (min-width: 768px) {
        .dashboard-grid {
          grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
        }
      }

      @media (min-width: 1024px) {
        .dashboard-grid {
          grid-template-columns: repeat(4, minmax(0, 1fr)) !important;
        }
      }
    </style>
    <script>
      document.addEventListener('DOMContentLoaded', function () {
        const grid = document.querySelector('.dashboard-grid');
        if (grid) {
          function updateGrid() {
            const width = window.innerWidth;
            if (width >= 1024) {
              grid.style.gridTemplateColumns = 'repeat(4, minmax(0, 1fr))';
            } else if (width >= 768) {
              grid.style.gridTemplateColumns = 'repeat(2, minmax(0, 1fr))';
            } else {
              grid.style.gridTemplateColumns = 'repeat(1, minmax(0, 1fr))';
            }
          }
          updateGrid();
          window.addEventListener('resize', updateGrid);
        }
      });
    </script>
    <!-- Guide Status Card -->
    <div class="bg-white overflow-hidden shadow rounded-lg" role="region" aria-labelledby="guide-status-heading"
      style="background-color: rgb(255, 255, 255); overflow: hidden; box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1); border-radius: 0.5rem;">
      <div class="p-5 text-center" style="padding: 1.25rem; text-align: center;">
        <div class="w-12 h-12 bg-primary-500 rounded-lg flex items-center justify-center mx-auto mb-3"
          style="width: 3rem; height: 3rem; background-color: rgb(59, 130, 246); border-radius: 0.5rem; display: flex; align-items: center; justify-content: center; margin-left: auto; margin-right: auto; margin-bottom: 0.75rem;">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <dl>
          <dt id="guide-status-heading" class="text-sm font-medium text-gray-500 mb-1" data-i18n="guide_status">Guide
            Status</dt>
          <dd class="text-lg font-medium text-gray-900" aria-describedby="guide-status-heading">
            {% if guide_uploaded %}
            <span class="text-success-600" data-i18n="status_uploaded">Uploaded</span>
            {% else %}
            <span class="text-gray-400" data-i18n="status_not_uploaded">Not uploaded</span>
            {% endif %}
          </dd>
        </dl>
      </div>
      <div class="bg-gray-50 px-5 py-3 text-center">
        <div class="text-sm">
          {% if guide_uploaded %}
          <span class="text-success-600 font-medium" data-i18n="ready_to_grade">Ready to grade</span>
          {% else %}
          <a href="{{ url_for('upload_guide') }}" class="text-primary-600 font-medium hover:text-primary-500"
            data-i18n="upload_required">Upload required</a>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Submissions Card -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5 text-center">
        <div class="w-12 h-12 bg-primary-500 rounded-lg flex items-center justify-center mx-auto mb-3">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
          </svg>
        </div>
        <dl>
          <dt class="text-sm font-medium text-gray-500 mb-1" data-i18n="submissions">Submissions</dt>
          <dd class="text-lg font-medium text-gray-900">
            {{ total_submissions }}
          </dd>
        </dl>
      </div>
      <div class="bg-gray-50 px-5 py-3 text-center">
        <div class="text-sm">
          <span class="text-gray-600 font-medium" id="processed-submissions-dashboard-count">{{ processed_submissions }}
            <span data-i18n="processed">processed</span></span>
        </div>
      </div>
    </div>

    <!-- Last Score Card -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5 text-center">
        <div class="w-12 h-12 bg-primary-500 rounded-lg flex items-center justify-center mx-auto mb-3">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
          </svg>
        </div>
        <dl>
          <dt class="text-sm font-medium text-gray-500 mb-1" data-i18n="last_score">Last Score</dt>
          <dd class="text-lg font-medium text-gray-900">
            {% if last_score > 0 %} {{ last_score }}% {% else %} -- {% endif %}
          </dd>
        </dl>
      </div>
      <div class="bg-gray-50 px-5 py-3 text-center">
        <div class="text-sm">
          {% if last_score > 0 %}
          <span class="text-gray-600 font-medium" data-i18n="latest_result">Latest result</span>
          {% else %}
          <span class="text-gray-600 font-medium" data-i18n="no_grades">No grades yet</span>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- System Status Card -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="p-5 text-center">
        <div class="w-12 h-12 bg-primary-500 rounded-lg flex items-center justify-center mx-auto mb-3">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <dl>
          <dt class="text-sm font-medium text-gray-500 mb-1" data-i18n="system_status">System Status</dt>
          <dd class="text-lg font-medium text-gray-900">
            {% if service_status.ocr_status and service_status.llm_status %}
            <span class="text-success-600" data-i18n="status_online">Online</span>
            {% else %}
            <span class="text-warning-600" data-i18n="status_limited">Limited</span>
            {% endif %}
          </dd>
        </dl>
      </div>
      <div class="bg-gray-50 px-5 py-3 text-center">
        <div class="text-sm">
          {% if service_status.ocr_status and service_status.llm_status %}
          <span class="text-success-600 font-medium" data-i18n="all_services_ready">All services ready</span>
          {% else %}
          <span class="text-warning-600 font-medium" data-i18n="some_services_offline">Some services offline</span>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  <!-- Main Action Cards -->
  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8 action-cards-grid"
    style="display: grid; gap: 1.5rem; margin-bottom: 2rem;">
    <style>
      /* Ensure cards have equal height and proper alignment */
      .action-cards-grid {
        display: grid !important;
        grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
        gap: 1.5rem !important;
      }

      @media (min-width: 768px) {
        .action-cards-grid {
          grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
        }
      }

      @media (min-width: 1024px) {
        .action-cards-grid {
          grid-template-columns: repeat(4, minmax(0, 1fr)) !important;
        }
      }

      .action-cards-grid>div {
        height: 100% !important;
        display: flex !important;
        flex-direction: column !important;
      }

      .action-cards-grid .card-content {
        flex: 1 !important;
        display: flex !important;
        flex-direction: column !important;
      }

      .action-cards-grid .card-actions {
        margin-top: auto !important;
      }
    </style>
    <script>
      document.addEventListener('DOMContentLoaded', function () {
        const actionGrid = document.querySelector('.action-cards-grid');
        if (actionGrid) {
          function updateActionGrid() {
            const width = window.innerWidth;
            if (width >= 1024) {
              actionGrid.style.gridTemplateColumns = 'repeat(4, minmax(0, 1fr))';
            } else if (width >= 768) {
              actionGrid.style.gridTemplateColumns = 'repeat(2, minmax(0, 1fr))';
            } else {
              actionGrid.style.gridTemplateColumns = 'repeat(1, minmax(0, 1fr))';
            }
          }
          updateActionGrid();
          window.addEventListener('resize', updateActionGrid);
        }
      });
    </script>
    <!-- Upload Marking Guide Card -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6 card-content">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-8 w-8 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          </div>
          <div class="ml-5">
            <h3 class="text-lg leading-6 font-medium text-gray-900" data-i18n="marking_guide">
              Marking Guide
            </h3>
            <p class="mt-2 text-sm text-gray-500" data-i18n="upload_marking_guide">Upload marking guide</p>
          </div>
        </div>
        <div class="mt-5 card-actions">
          {% if not guide_uploaded %}
          <a href="{{ url_for('upload_guide') }}"
            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            data-i18n="upload_guide">
            Upload Guide
          </a>
          {% else %}
          <div class="text-center">
            <div class="text-sm text-success-600 font-medium mb-2" data-i18n="guide_uploaded">
              ✓ Guide Uploaded
            </div>
            <a href="{{ url_for('upload_guide') }}" class="text-sm text-primary-600 hover:text-primary-500"
              data-i18n="upload_new_guide">Upload New Guide</a>
          </div>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Upload Submission Card -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6 card-content">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-8 w-8 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
          </div>
          <div class="ml-5">
            <h3 class="text-lg leading-6 font-medium text-gray-900" data-i18n="upload_submission">
              Upload Submission
            </h3>
            <p class="mt-2 text-sm text-gray-500" data-i18n="upload_student_work">Upload student work</p>
          </div>
        </div>
        <div class="mt-5 card-actions">
          {% if not guide_uploaded %}
          <div class="text-center">
            <div class="text-sm text-gray-500 mb-2" data-i18n="upload_guide_first">Upload guide first</div>
            <button disabled
              class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-400 bg-gray-100 cursor-not-allowed"
              data-i18n="upload_submission">
              Upload Submission
            </button>
          </div>
          {% else %}
          <a href="{{ url_for('upload_submission') }}"
            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-success-600 hover:bg-success-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-success-500"
            data-i18n="upload_submission">
            Upload Submission
          </a>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- AI Processing Card -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6 card-content">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-8 w-8 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
            </svg>
          </div>
          <div class="ml-5">
            <h3 class="text-lg leading-6 font-medium text-gray-900">
              AI Processing
            </h3>
            <p class="mt-2 text-sm text-gray-500">Choose processing method</p>
          </div>
        </div>
        <div class="mt-5 card-actions">
          {% if guide_uploaded and total_submissions > 0 %}
          <!-- Enhanced AI Processing -->
          <a href="{{ url_for('enhanced_processing') }}"
            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500 transition-all duration-200">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z">
              </path>
            </svg>
            Enhanced AI Pipeline
          </a>

          {% else %}
          <div class="text-center">
            <div class="text-sm text-gray-500 mb-2">
              {% if not guide_uploaded %} Need guide & submissions {% elif
              total_submissions == 0 %} Need submissions {% endif %}
            </div>
            <button disabled
              class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-400 bg-gray-100 cursor-not-allowed">
              Enhanced Pipeline
            </button>
          </div>
          {% endif %}
        </div>
      </div>
    </div>

    <!-- Results Card -->
    <div class="bg-white overflow-hidden shadow rounded-lg">
      <div class="px-4 py-5 sm:p-6 card-content">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <svg class="h-8 w-8 text-primary-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <div class="ml-5">
            <h3 class="text-lg leading-6 font-medium text-gray-900">Results</h3>
            <p class="mt-2 text-sm text-gray-500">View and manage</p>
          </div>
        </div>
        <div class="mt-5 card-actions">
          {% if session.get('last_grading_result') %}
          <a href="{{ url_for('view_results') }}"
            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 mb-2">
            View Results
          </a>
          {% else %}
          {% if submissions %}
          <div class="text-center mb-2">
            <a href="{{ url_for('view_results') }}"
              class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
              View Results
            </a>
          </div>
          {% else %}
          <div class="text-center mb-2">
            <div class="text-sm text-gray-500 mb-2">
              <span class="spinner-border spinner-border-sm" role="status"></span>
              Processing submissions...
            </div>
          </div>
          {% endif %}
          {% if total_submissions > 0 %}
          <a href="{{ url_for('view_submissions') }}"
            class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
            View Submissions
          </a>
          {% endif %}
        </div>
      </div>
    </div>
  </div>

  <!-- Workflow Process Section -->
  <div class="bg-white shadow rounded-lg mb-8">
    <div class="px-6 py-4 border-b border-gray-200">
      <h3 class="text-lg leading-6 font-medium text-gray-900">Grading Workflow</h3>
      <p class="mt-1 text-sm text-gray-500">Follow these steps to grade submissions efficiently</p>
    </div>
    <div class="p-6">
      <div class="flow-chart">
        <!-- Step 1: Upload Guide -->
        <div class="flex items-center mb-6">
          <div class="flex-shrink-0">
            <div
              class="w-10 h-10 rounded-full flex items-center justify-center {% if guide_uploaded %}bg-green-100 text-green-600{% else %}bg-gray-100 text-gray-400{% endif %}">
              {% if guide_uploaded %}
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              {% else %}
              <span class="text-sm font-medium">1</span>
              {% endif %}
            </div>
          </div>
          <div class="ml-4 flex-1">
            <h4 class="text-sm font-medium text-gray-900">Upload Marking Guide</h4>
            <p class="text-sm text-gray-500">Upload your marking rubric and answer key</p>
            {% if not guide_uploaded %}
            <a href="{{ url_for('upload_guide') }}" class="text-sm text-primary-600 hover:text-primary-500">Upload now
              →</a>
            {% endif %}
          </div>
          <div class="flex-shrink-0">
            {% if guide_uploaded %}
            <span
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">Complete</span>
            {% else %}
            <span
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Pending</span>
            {% endif %}
          </div>
        </div>

        <!-- Arrow -->
        {% if guide_uploaded %}
        <div class="flex justify-center mb-6">
          <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
          </svg>
        </div>
        {% endif %}

        <!-- Step 2: Upload Submissions -->
        <div class="flex items-center mb-6">
          <div class="flex-shrink-0">
            <div
              class="w-10 h-10 rounded-full flex items-center justify-center {% if total_submissions > 0 %}bg-green-100 text-green-600{% elif guide_uploaded %}bg-blue-100 text-blue-600{% else %}bg-gray-100 text-gray-400{% endif %}">
              {% if total_submissions > 0 %}
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              {% elif guide_uploaded %}
              <span class="text-sm font-medium">2</span>
              {% else %}
              <span class="text-sm font-medium">2</span>
              {% endif %}
            </div>
          </div>
          <div class="ml-4 flex-1">
            <h4 class="text-sm font-medium text-gray-900">Upload Student Submissions</h4>
            <p class="text-sm text-gray-500">Upload student work to be graded ({{ total_submissions }} uploaded)</p>
            {% if guide_uploaded and total_submissions == 0 %}
            <a href="{{ url_for('upload_submission') }}" class="text-sm text-primary-600 hover:text-primary-500">Upload
              submissions →</a>
            {% endif %}
          </div>
          <div class="flex-shrink-0">
            {% if total_submissions > 0 %}
            <span
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">{{
              total_submissions }} files</span>
            {% elif guide_uploaded %}
            <span
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Ready</span>
            {% else %}
            <span
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Waiting</span>
            {% endif %}
          </div>
        </div>

        <!-- Arrow -->
        {% if guide_uploaded and total_submissions > 0 %}
        <div class="flex justify-center mb-6">
          <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
          </svg>
        </div>
        {% endif %}

        <!-- Step 3: Choose Processing Method -->
        <div class="flex items-center mb-6">
          <div class="flex-shrink-0">
            <div
              class="w-10 h-10 rounded-full flex items-center justify-center {% if guide_uploaded and total_submissions > 0 %}bg-blue-100 text-blue-600{% else %}bg-gray-100 text-gray-400{% endif %}">
              <span class="text-sm font-medium">3</span>
            </div>
          </div>
          <div class="ml-4 flex-1">
            <h4 class="text-sm font-medium text-gray-900">Start AI Processing</h4>
            <p class="text-sm text-gray-500">Use enhanced AI pipeline for grading</p>
            {% if guide_uploaded and total_submissions > 0 %}
            <div class="mt-2">
              <a href="{{ url_for('enhanced_processing') }}"
                class="text-sm text-emerald-600 hover:text-emerald-500">Enhanced Pipeline →</a>
            </div>
            {% endif %}
          </div>
          <div class="flex-shrink-0">
            {% if guide_uploaded and total_submissions > 0 %}
            <span
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">Ready</span>
            {% else %}
            <span
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Waiting</span>
            {% endif %}
          </div>
        </div>

        <!-- Arrow -->
        {% if processed_submissions > 0 %}
        <div class="flex justify-center mb-6">
          <svg class="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
          </svg>
        </div>
        {% endif %}

        <!-- Step 4: View Results -->
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div
              class="w-10 h-10 rounded-full flex items-center justify-center {% if processed_submissions > 0 %}bg-green-100 text-green-600{% else %}bg-gray-100 text-gray-400{% endif %}">
              {% if processed_submissions > 0 %}
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
              {% else %}
              <span class="text-sm font-medium">4</span>
              {% endif %}
            </div>
          </div>
          <div class="ml-4 flex-1">
            <h4 class="text-sm font-medium text-gray-900">Review Results</h4>
            <p class="text-sm text-gray-500">View graded submissions and export results</p>
            {% if processed_submissions > 0 %}
            <a href="{{ url_for('view_results') }}" class="text-sm text-primary-600 hover:text-primary-500">View results
              →</a>
            {% endif %}
          </div>
          <div class="flex-shrink-0">
            {% if processed_submissions > 0 %}
            <span
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">{{
              processed_submissions }} graded</span>
            {% else %}
            <span
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">Pending</span>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Recent Activity -->
  {% if recent_activity %}
  <div class="bg-white shadow rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">
        Recent Activity
      </h3>
      <div class="flow-root">
        <ul class="-mb-8">
          {% for activity in recent_activity %}
          <li>
            <div class="relative pb-8">
              {% if not loop.last %}
              <span class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true"></span>
              {% endif %}
              <div class="relative flex space-x-3">
                <div>
                  <span class="h-8 w-8 rounded-full bg-primary-500 flex items-center justify-center ring-8 ring-white">
                    {% if activity.icon == 'document' %}
                    <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    {% elif activity.icon == 'upload' %}
                    <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    {% elif activity.icon == 'check' %}
                    <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    {% elif activity.icon == 'star' %}
                    <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                    </svg>
                    {% else %}
                    <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {% endif %}
                  </span>
                </div>
                <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                  <div>
                    <p class="text-sm text-gray-500">{{ activity.message }}</p>
                  </div>
                  <div class="text-right text-sm whitespace-nowrap text-gray-500">
                    <time datetime="{{ activity.timestamp }}">{{ activity.timestamp[:10] }}</time>
                  </div>
                  {% if activity.type == 'submission_deleted' or activity.type == 'grading_result_deleted' %}
                  <div class="flex-shrink-0 ml-2">
                    <button type="button"
                      class="delete-activity-btn inline-flex items-center p-1 border border-transparent rounded-full shadow-sm text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
                      data-id="{{ activity.id }}" data-type="{{ activity.type }}">
                      <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                        <path fill-rule="evenodd"
                          d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm6 0a1 1 0 012 0v6a1 1 0 11-2 0V8z"
                          clip-rule="evenodd" />
                      </svg>
                    </button>
                  </div>
                  {% endif %}
                </div>
              </div>
            </div>
          </li>
          {% endfor %}
        </ul>
      </div>
      {% endif %}
      {% endif %}
      {% endblock %}

      {% block extra_js %}
      <script>
        document.addEventListener("DOMContentLoaded", function () {
          // Clear any lingering button loading states when dashboard loads
          const clearButtonLoadingStates = () => {
            const processUnifiedAIBtn = document.getElementById("process-unified-ai");
            if (processUnifiedAIBtn && processUnifiedAIBtn.disabled) {
              try {
                if (typeof ExamGrader !== 'undefined' && ExamGrader.utils && ExamGrader.utils.hideButtonLoading) {
                  ExamGrader.utils.hideButtonLoading(processUnifiedAIBtn);
                }
                processUnifiedAIBtn.disabled = false;
                console.log('Cleared lingering loading state from AI Processing button');
              } catch (error) {
                console.error('Error clearing button loading state:', error);
                processUnifiedAIBtn.disabled = false;
              }
            }
          };

          // Clear loading states immediately
          clearButtonLoadingStates();

          // Ensure CSRF token is available
          const refreshCsrfToken = async () => {
            try {
              if (typeof ExamGrader === 'undefined' || !ExamGrader.csrf) {
                console.warn('ExamGrader.csrf not available, skipping token refresh');
                return;
              }
              const token = await ExamGrader.csrf.refreshToken();
              if (token) {
                console.log('Dashboard page CSRF token refresh successful');
              } else {
                console.warn('Dashboard page CSRF token refresh failed');
              }
            } catch (error) {
              console.error('Error refreshing CSRF token on dashboard:', error);
            }
          };

          // Refresh token immediately
          refreshCsrfToken();
          // Unified AI Processing Button
          const processUnifiedAIBtn = document.getElementById("process-unified-ai");
          if (processUnifiedAIBtn) {
            processUnifiedAIBtn.addEventListener("click", async function () {
              try {
                if (typeof ExamGrader !== 'undefined' && ExamGrader.utils && ExamGrader.utils.showButtonLoading) {
                  ExamGrader.utils.showButtonLoading(this, "Processing...");
                }
              } catch (error) {
                console.error('Error showing button loading state:', error);
              }
              this.disabled = true;

              try {
                console.log("Starting unified AI processing...");

                // Get CSRF token from meta tag
                const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

                // Start the unified AI processing
                const response = await fetch('/api/process-unified-ai', {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                  },
                  body: JSON.stringify({}),
                  credentials: 'same-origin'
                });

                console.log("Response status:", response.status);

                if (!response.ok) {
                  const errorText = await response.text();
                  console.error("Response error text:", errorText);
                  throw new Error(`HTTP ${response.status}: ${errorText}`);
                }

                const data = await response.json();
                console.log("Processing result:", data);

                if (data.success) {
                  // Check if we have a progress_id
                  if (data.progress_id) {
                    // Redirect to the unified processing page with the progress_id
                    window.location.href = `/unified-processing?progress_id=${data.progress_id}`;
                    return;
                  }

                  const summary = data.summary || {};
                  const avgPercentage = summary.average_percentage || 0;
                  const processingTime = summary.processing_time || 0;

                  try {
                    if (typeof ExamGrader !== 'undefined' && ExamGrader.notificationManager) {
                      ExamGrader.notificationManager.notify(
                        `AI processing completed! Average score: ${avgPercentage}% (${processingTime}s)`,
                        "success"
                      );
                    }
                  } catch (error) {
                    console.error('Error showing success notification:', error);
                  }

                  setTimeout(() => window.location.href = '/results', 2000);
                } else {
                  throw new Error(data.error || "AI processing failed");
                }
              } catch (error) {
                console.error("Error:", error);
                try {
                  if (typeof ExamGrader !== 'undefined' && ExamGrader.notificationManager) {
                    ExamGrader.notificationManager.notify(`AI processing failed: ${error.message}`, "error");
                  }
                } catch (notifyError) {
                  console.error('Error showing error notification:', notifyError);
                }
              } finally {
                try {
                  if (typeof ExamGrader !== 'undefined' && ExamGrader.utils && ExamGrader.utils.hideButtonLoading) {
                    ExamGrader.utils.hideButtonLoading(this, "AI Processing");
                  }
                } catch (hideError) {
                  console.error('Error hiding button loading state:', hideError);
                }
                this.disabled = false;
              }
            });
          }

          // Delete Activity Button
          document.querySelectorAll('.delete-activity-btn').forEach(button => {
            button.addEventListener('click', async function () {
              const id = this.dataset.id;
              const type = this.dataset.type;
              let endpoint = '';

              if (type === 'submission_deleted') {
                endpoint = '/api/delete-submission';
              } else if (type === 'grading_result_deleted') {
                endpoint = '/api/delete-grading-result';
              } else {
                console.error('Unknown activity type for deletion:', type);
                return;
              }

              if (!confirm('Are you sure you want to delete this item?')) {
                return;
              }

              try {
                // Get CSRF token from meta tag
                const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

                const response = await fetch(endpoint, {
                  method: 'POST',
                  headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                  },
                  body: JSON.stringify({ [type.includes('submission') ? 'submission_id' : 'grading_result_id']: id }),
                  credentials: 'same-origin'
                });

                const data = await response.json();

                if (data.success) {
                  try {
                    if (typeof ExamGrader !== 'undefined' && ExamGrader.notificationManager) {
                      ExamGrader.notificationManager.notify(data.message, 'success');
                    }
                  } catch (notifyError) {
                    console.error('Error showing success notification:', notifyError);
                  }
                  setTimeout(() => location.reload(), 1000);
                } else {
                  try {
                    if (typeof ExamGrader !== 'undefined' && ExamGrader.notificationManager) {
                      ExamGrader.notificationManager.notify(data.message, 'error');
                    }
                  } catch (notifyError) {
                    console.error('Error showing error notification:', notifyError);
                  }
                }
              } catch (error) {
                console.error('Error deleting item:', error);
                try {
                  if (typeof ExamGrader !== 'undefined' && ExamGrader.notificationManager) {
                    ExamGrader.notificationManager.notify('Error deleting item: ' + error.message, 'error');
                  }
                } catch (notifyError) {
                  console.error('Error showing error notification:', notifyError);
                }
              }
            });
          });
        });
      </script>
    </div>
  </div>
  {% endblock %}